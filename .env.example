# Apex/BioScan Environment Configuration

# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# Google Sign-In
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_google_web_client_id.apps.googleusercontent.com

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://your_project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Turso Configuration (for production)
EXPO_PUBLIC_TURSO_URL=libsql://your_database.turso.io
EXPO_PUBLIC_TURSO_AUTH_TOKEN=your_turso_auth_token

# Google Gemini AI
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key

# Third-party API Keys
EXPO_PUBLIC_OPEN_FOOD_FACTS_API_KEY=your_off_api_key
EXPO_PUBLIC_PLANT_ID_API_KEY=your_plant_id_api_key
EXPO_PUBLIC_INATURALIST_API_KEY=your_inaturalist_api_key
EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_token

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_BASE_URL=https://api.apex-bioscan.com
EXPO_PUBLIC_SENTRY_DSN=your_sentry_dsn
