# 🔧 Apex Hybrid Backend Setup Guide

This guide walks you through setting up the hybrid backend architecture for Apex/BioScan, which combines Firebase Auth, Supabase Database, and Turso local storage.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    APEX HYBRID BACKEND                      │
├─────────────────────────────────────────────────────────────┤
│  🔐 Firebase Auth (Multi-provider Authentication)           │
│  ├── Google Sign-In                                        │
│  ├── Apple Sign-In                                         │
│  ├── Email/Password                                        │
│  └── Push Notifications (FCM)                              │
├─────────────────────────────────────────────────────────────┤
│  💾 Turso SQLite (Local Offline-First Storage)             │
│  ├── Local Data Caching                                    │
│  ├── Offline Operations                                    │
│  ├── Sync Queue Management                                 │
│  └── Conflict Resolution                                   │
├─────────────────────────────────────────────────────────────┤
│  ☁️ Supabase PostgreSQL (Cloud Database & Real-time)       │
│  ├── User Profiles & Settings                              │
│  ├── Scan History & Collections                            │
│  ├── Community Features                                    │
│  ├── Real-time Subscriptions                               │
│  └── Edge Functions (AI Processing)                        │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Setup Instructions

### 1. Firebase Setup

#### 1.1 Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project: "apex-bioscan"
3. Enable Google Analytics (optional)

#### 1.2 Configure Authentication
1. Go to Authentication → Sign-in method
2. Enable the following providers:
   - **Email/Password**: Enable
   - **Google**: Enable and configure OAuth consent screen
   - **Apple**: Enable (iOS only)

#### 1.3 Configure Cloud Messaging
1. Go to Cloud Messaging
2. Generate server key for push notifications
3. Configure APNs for iOS (upload certificates)

#### 1.4 Get Configuration
1. Go to Project Settings → General
2. Add iOS app with bundle ID: `com.apex.bioscan`
3. Add Android app with package name: `com.apex.bioscan`
4. Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)

### 2. Supabase Setup

#### 2.1 Create Supabase Project
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create new project: "apex-bioscan"
3. Choose region closest to your users

#### 2.2 Database Schema
Run the following SQL in Supabase SQL Editor:

```sql
-- Enable RLS
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium')),
  preferences JSONB DEFAULT '{"theme": "auto", "language": "en", "notifications": true, "location_sharing": false}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scans table
CREATE TABLE scans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('food', 'plant', 'animal', 'rock', 'coin', 'insect')),
  identification JSONB NOT NULL,
  location JSONB,
  metadata JSONB NOT NULL,
  fact_sheet JSONB,
  user_notes TEXT,
  tags TEXT[],
  is_favorite BOOLEAN DEFAULT FALSE,
  is_verified BOOLEAN DEFAULT FALSE,
  verification_source TEXT CHECK (verification_source IN ('community', 'expert', 'ai')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Todos table
CREATE TABLE todos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT,
  is_completed BOOLEAN DEFAULT FALSE,
  scan_id UUID REFERENCES scans(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Achievements table
CREATE TABLE achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  achievement_type TEXT NOT NULL,
  achievement_data JSONB NOT NULL,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own scans" ON scans FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own scans" ON scans FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own scans" ON scans FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own scans" ON scans FOR DELETE USING (auth.uid() = user_id);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE scans ENABLE ROW LEVEL SECURITY;
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;

-- Indexes for performance
CREATE INDEX idx_scans_user_id ON scans(user_id);
CREATE INDEX idx_scans_category ON scans(category);
CREATE INDEX idx_scans_created_at ON scans(created_at);
CREATE INDEX idx_todos_user_id ON todos(user_id);
CREATE INDEX idx_achievements_user_id ON achievements(user_id);
```

#### 2.3 Storage Setup
1. Go to Storage → Create bucket: "images"
2. Set bucket to public
3. Create RLS policies for image uploads

#### 2.4 Edge Functions (Optional)
Create Edge Functions for AI processing:

```typescript
// supabase/functions/process-scan/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

serve(async (req) => {
  const { imageUrl, category } = await req.json()
  
  // Process with Gemini API
  // Return enhanced fact sheet
  
  return new Response(
    JSON.stringify({ success: true, data: result }),
    { headers: { "Content-Type": "application/json" } },
  )
})
```

### 3. Turso Setup

#### 3.1 Create Turso Database
```bash
# Install Turso CLI
curl -sSfL https://get.tur.so/install.sh | bash

# Create database
turso db create apex-bioscan

# Get database URL and auth token
turso db show apex-bioscan
turso db tokens create apex-bioscan
```

#### 3.2 Local Development
For local development, Turso will use a local SQLite file. No additional setup required.

### 4. Environment Configuration

Create `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Fill in all the required API keys and configuration values.

### 5. Mobile App Configuration

#### 5.1 iOS Configuration
1. Add `GoogleService-Info.plist` to `ios/` directory
2. Configure URL schemes in `Info.plist`
3. Add Apple Sign-In capability

#### 5.2 Android Configuration
1. Add `google-services.json` to `android/app/`
2. Configure Firebase in `android/build.gradle`

## 🔄 Data Sync Strategy

### Offline-First Approach
1. **All operations work offline** using Turso local database
2. **Optimistic updates** provide immediate UI feedback
3. **Background sync** occurs when online
4. **Conflict resolution** handles simultaneous edits

### Sync Flow
```
User Action → Turso Local → Sync Queue → Background Sync → Supabase Cloud
                        ↓
                 Immediate UI Update (Optimistic)
                        ↓
                 Real-time Updates ← Supabase Subscriptions
```

### Conflict Resolution
- **Local wins**: For user preferences and notes
- **Remote wins**: For community data and verifications
- **Merge**: For compatible changes (tags, favorites)
- **Manual**: For complex conflicts (rare)

## 🔒 Security Considerations

### Authentication Flow
```
User → Firebase Auth → JWT Token → Supabase RLS → Protected Resources
                   ↓
              FCM Token → Push Notifications
```

### Data Protection
- **Firebase Auth** handles all authentication
- **Supabase RLS** protects data access
- **Turso encryption** secures local data
- **HTTPS/TLS** for all network communication

## 🧪 Testing the Setup

### 1. Test Firebase Auth
```typescript
import { firebaseService } from './src/services/firebaseService';

// Test email sign-in
const result = await firebaseService.signInWithEmail('<EMAIL>', 'password');
console.log('Auth result:', result);
```

### 2. Test Turso Local Storage
```typescript
import { tursoService } from './src/services/tursoService';

// Test local database
await tursoService.initialize();
const scans = await tursoService.getScans('user-id');
console.log('Local scans:', scans);
```

### 3. Test Supabase Sync
```typescript
import { syncService } from './src/services/syncService';

// Test sync
await syncService.initialize();
await syncService.forcSync();
console.log('Sync completed');
```

## 🚨 Troubleshooting

### Common Issues
1. **Firebase Auth not working**: Check API keys and bundle IDs
2. **Supabase RLS blocking requests**: Verify JWT token setup
3. **Turso sync failing**: Check network connectivity and auth tokens
4. **Push notifications not received**: Verify FCM configuration

### Debug Tools
- Firebase Console → Authentication
- Supabase Dashboard → Logs
- Turso CLI: `turso db shell apex-bioscan`
- React Native Debugger for network requests

## 📚 Next Steps

1. **Implement UI components** using the new stores
2. **Add real-time features** with Supabase subscriptions
3. **Optimize sync performance** with batching and compression
4. **Add offline indicators** in the UI
5. **Implement push notification handlers**

---

This hybrid backend provides the best of all worlds: Firebase's robust authentication, Supabase's powerful database features, and Turso's excellent offline capabilities.
