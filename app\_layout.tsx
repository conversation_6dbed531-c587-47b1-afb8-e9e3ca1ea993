import { useFonts } from 'expo-font';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { initializationService } from '@/src/services/initializationService';
import { useAuthStore } from '@/src/stores/authStoreFirebase';
import { ApexThemeProvider } from '@/src/theme/ThemeProvider';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        const status = await initializationService.initialize();
        if (status.overall) {
          setIsInitialized(true);
        } else {
          setInitError(status.error || 'Initialization failed');
        }
      } catch (error) {
        setInitError(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    initializeApp();
  }, []);

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (isInitialized && !isAuthenticated) {
      router.replace('/auth');
    }
  }, [isInitialized, isAuthenticated]);

  if (!loaded) {
    return null;
  }

  if (!isInitialized) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colorScheme === 'dark' ? '#000' : '#fff'
      }}>
        <ActivityIndicator size="large" color={colorScheme === 'dark' ? '#fff' : '#000'} />
        <Text style={{
          marginTop: 16,
          color: colorScheme === 'dark' ? '#fff' : '#000',
          fontSize: 16,
          textAlign: 'center',
          paddingHorizontal: 20,
        }}>
          {initError ? `Error: ${initError}` : 'Initializing Apex...'}
        </Text>
        {initError && (
          <Text style={{
            marginTop: 8,
            color: '#FF6B6B',
            fontSize: 14,
            textAlign: 'center',
            paddingHorizontal: 20,
          }}>
            Please check your internet connection and restart the app.
          </Text>
        )}
      </View>
    );
  }

  return (
    <ApexThemeProvider>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="auth" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ApexThemeProvider>
  );
}
