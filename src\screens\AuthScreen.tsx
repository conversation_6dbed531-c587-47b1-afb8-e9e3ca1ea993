import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  Text,
  Button,
  TextInput,
  Card,
  ActivityIndicator,
  Divider,
  IconButton,
  Snackbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { useAuthStore } from '../stores/authStoreFirebase';
import { useTheme } from '../theme/ThemeProvider';
import { commonStyles } from '../theme/theme';

type AuthMode = 'signin' | 'signup' | 'forgot';

export default function AuthScreen() {
  const { paperTheme } = useTheme();
  const {
    signInWithEmail,
    signUpWithEmail,
    signInWithGoogle,
    signInWithApple,
    sendPasswordReset,
    isLoading,
    isAuthenticated,
  } = useAuthStore();

  const [mode, setMode] = useState<AuthMode>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated]);

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
  };

  const handleEmailAuth = async () => {
    if (!email || !password) {
      showSnackbar('Please fill in all fields');
      return;
    }

    if (mode === 'signup') {
      if (password !== confirmPassword) {
        showSnackbar('Passwords do not match');
        return;
      }
      if (password.length < 6) {
        showSnackbar('Password must be at least 6 characters');
        return;
      }
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      let result;
      if (mode === 'signin') {
        result = await signInWithEmail(email, password);
      } else if (mode === 'signup') {
        result = await signUpWithEmail(email, password, username);
      } else {
        result = await sendPasswordReset(email);
      }

      if (result.success) {
        if (mode === 'forgot') {
          showSnackbar('Password reset email sent!');
          setMode('signin');
        } else {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          // Navigation handled by useEffect
        }
      } else {
        showSnackbar(result.error || 'Authentication failed');
      }
    } catch (error) {
      showSnackbar('An unexpected error occurred');
    }
  };

  const handleGoogleSignIn = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      const result = await signInWithGoogle();
      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        showSnackbar(result.error || 'Google sign-in failed');
      }
    } catch (error) {
      showSnackbar('Google sign-in failed');
    }
  };

  const handleAppleSignIn = async () => {
    if (Platform.OS !== 'ios') {
      showSnackbar('Apple Sign-In is only available on iOS');
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      const result = await signInWithApple();
      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        showSnackbar(result.error || 'Apple sign-in failed');
      }
    } catch (error) {
      showSnackbar('Apple sign-in failed');
    }
  };

  const renderModeContent = () => {
    switch (mode) {
      case 'signin':
        return (
          <>
            <Text variant="headlineMedium" style={styles.title}>
              Welcome Back
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Sign in to continue your exploration
            </Text>
          </>
        );
      case 'signup':
        return (
          <>
            <Text variant="headlineMedium" style={styles.title}>
              Join Apex
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Start your AI-powered discovery journey
            </Text>
          </>
        );
      case 'forgot':
        return (
          <>
            <Text variant="headlineMedium" style={styles.title}>
              Reset Password
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Enter your email to receive reset instructions
            </Text>
          </>
        );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: paperTheme.colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text variant="displaySmall" style={[styles.logo, { color: paperTheme.colors.primary }]}>
              🧠 Apex
            </Text>
            {renderModeContent()}
          </View>

          {/* Auth Form */}
          <Card style={[styles.card, commonStyles.shadow]}>
            <Card.Content>
              {/* Email Input */}
              <TextInput
                label="Email"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                style={commonStyles.input}
                disabled={isLoading}
              />

              {/* Username Input (Sign Up only) */}
              {mode === 'signup' && (
                <TextInput
                  label="Username (optional)"
                  value={username}
                  onChangeText={setUsername}
                  mode="outlined"
                  autoCapitalize="none"
                  style={commonStyles.input}
                  disabled={isLoading}
                />
              )}

              {/* Password Input */}
              {mode !== 'forgot' && (
                <TextInput
                  label="Password"
                  value={password}
                  onChangeText={setPassword}
                  mode="outlined"
                  secureTextEntry={!showPassword}
                  style={commonStyles.input}
                  disabled={isLoading}
                  right={
                    <TextInput.Icon
                      icon={showPassword ? 'eye-off' : 'eye'}
                      onPress={() => setShowPassword(!showPassword)}
                    />
                  }
                />
              )}

              {/* Confirm Password Input (Sign Up only) */}
              {mode === 'signup' && (
                <TextInput
                  label="Confirm Password"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  mode="outlined"
                  secureTextEntry={!showPassword}
                  style={commonStyles.input}
                  disabled={isLoading}
                />
              )}

              {/* Primary Action Button */}
              <Button
                mode="contained"
                onPress={handleEmailAuth}
                style={[commonStyles.button, styles.primaryButton]}
                disabled={isLoading}
                loading={isLoading}
              >
                {mode === 'signin' ? 'Sign In' : mode === 'signup' ? 'Create Account' : 'Send Reset Email'}
              </Button>

              {/* Social Sign In (not for forgot password) */}
              {mode !== 'forgot' && (
                <>
                  <Divider style={styles.divider} />
                  
                  <View style={styles.socialContainer}>
                    <Button
                      mode="outlined"
                      onPress={handleGoogleSignIn}
                      style={[commonStyles.button, styles.socialButton]}
                      icon="google"
                      disabled={isLoading}
                    >
                      Google
                    </Button>
                    
                    {Platform.OS === 'ios' && (
                      <Button
                        mode="outlined"
                        onPress={handleAppleSignIn}
                        style={[commonStyles.button, styles.socialButton]}
                        icon="apple"
                        disabled={isLoading}
                      >
                        Apple
                      </Button>
                    )}
                  </View>
                </>
              )}
            </Card.Content>
          </Card>

          {/* Mode Switch Links */}
          <View style={styles.linksContainer}>
            {mode === 'signin' && (
              <>
                <Button
                  mode="text"
                  onPress={() => setMode('forgot')}
                  disabled={isLoading}
                >
                  Forgot Password?
                </Button>
                <Button
                  mode="text"
                  onPress={() => setMode('signup')}
                  disabled={isLoading}
                >
                  Don't have an account? Sign Up
                </Button>
              </>
            )}
            
            {mode === 'signup' && (
              <Button
                mode="text"
                onPress={() => setMode('signin')}
                disabled={isLoading}
              >
                Already have an account? Sign In
              </Button>
            )}
            
            {mode === 'forgot' && (
              <Button
                mode="text"
                onPress={() => setMode('signin')}
                disabled={isLoading}
              >
                Back to Sign In
              </Button>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Snackbar for messages */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
        action={{
          label: 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    marginTop: 32,
  },
  logo: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  card: {
    marginBottom: 24,
  },
  primaryButton: {
    marginTop: 16,
  },
  divider: {
    marginVertical: 16,
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  socialButton: {
    flex: 1,
  },
  linksContainer: {
    alignItems: 'center',
    gap: 8,
  },
});
