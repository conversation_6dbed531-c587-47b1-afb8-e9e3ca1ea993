import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    StyleSheet,
    View,
} from 'react-native';
import {
    ActivityIndicator,
    Button,
    Card,
    Chip,
    IconButton,
    List,
    Modal,
    Portal,
    Text,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { geminiService } from '../services/geminiService';
import { useAuthStore } from '../stores/authStoreFirebase';
import { useScanStore } from '../stores/scanStoreTurso';
import { commonStyles } from '../theme/theme';
import { useTheme } from '../theme/ThemeProvider';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

type ScanMethod = 'camera' | 'gallery' | 'voice';
type CategoryFilter = 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect' | null;

export default function CameraScanScreen() {
  const { paperTheme } = useTheme();
  const { user } = useAuthStore();
  const { addScan, setProcessing, isProcessing, isOnline } = useScanStore();
  
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('back');
  const [flash, setFlash] = useState(false);
  const [scanMethod, setScanMethod] = useState<ScanMethod>('camera');
  const [categoryFilter, setCategoryFilter] = useState<CategoryFilter>(null);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  
  const cameraRef = useRef<CameraView>(null);

  useEffect(() => {
    // Request camera permissions on mount
    if (!permission?.granted) {
      requestPermission();
    }
  }, [permission]);

  const categories = [
    { key: 'food', label: 'Food', icon: '🍎' },
    { key: 'plant', label: 'Plants', icon: '🌱' },
    { key: 'animal', label: 'Animals', icon: '🦋' },
    { key: 'rock', label: 'Rocks & Minerals', icon: '💎' },
    { key: 'coin', label: 'Coins', icon: '🪙' },
    { key: 'insect', label: 'Insects', icon: '🐛' },
  ];

  const handleCapture = async () => {
    if (!cameraRef.current || isCapturing || isProcessing) return;
    if (!user) {
      Alert.alert('Authentication Required', 'Please sign in to scan items');
      return;
    }

    setIsCapturing(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
      });

      if (photo) {
        await processImage(photo.uri, 'camera');
      }
    } catch (error) {
      console.error('Camera capture error:', error);
      Alert.alert('Capture Failed', 'Failed to take photo. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  const handleGalleryPick = async () => {
    if (isProcessing) return;
    if (!user) {
      Alert.alert('Authentication Required', 'Please sign in to scan items');
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processImage(result.assets[0].uri, 'gallery');
      }
    } catch (error) {
      console.error('Gallery pick error:', error);
      Alert.alert('Gallery Error', 'Failed to select image from gallery.');
    }
  };

  const processImage = async (imageUri: string, method: ScanMethod) => {
    setProcessing(true);
    
    try {
      // Show processing feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Get user location (optional)
      let location;
      try {
        // TODO: Implement location services
        // const locationResult = await Location.getCurrentPositionAsync({});
        // location = {
        //   latitude: locationResult.coords.latitude,
        //   longitude: locationResult.coords.longitude,
        // };
      } catch (error) {
        console.log('Location not available');
      }

      // Call Gemini API for identification
      const identification = await geminiService.identifyImage(imageUri, {
        method,
        location,
        region: 'US', // TODO: Get from user settings
        language: 'en',
      });

      // Create scan result
      const scanResult = {
        id: `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: user.id,
        image_uri: imageUri,
        category: identification.category,
        identification: {
          name: identification.name,
          scientific_name: identification.scientific_name,
          confidence: identification.confidence,
          alternatives: identification.alternatives,
        },
        location,
        metadata: {
          scan_method: method,
          timestamp: new Date().toISOString(),
          device_info: {
            platform: Platform.OS,
            category_filter: categoryFilter,
          },
        },
        fact_sheet: identification.fact_sheet,
        is_favorite: false,
        is_verified: false,
      };

      // Add to store (will save to Turso and sync to Supabase)
      await addScan(scanResult);

      // Navigate to results screen
      router.push({
        pathname: '/(tabs)/scan-results',
        params: { scanId: scanResult.id },
      });

    } catch (error) {
      console.error('Image processing error:', error);
      Alert.alert(
        'Identification Failed',
        'Failed to identify the object. Please check your connection and try again.',
        [
          { text: 'Retry', onPress: () => processImage(imageUri, method) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    } finally {
      setProcessing(false);
    }
  };

  const toggleFlash = () => {
    setFlash(!flash);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const toggleCamera = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  if (!permission) {
    return (
      <View style={[commonStyles.centerContainer, { backgroundColor: paperTheme.colors.background }]}>
        <ActivityIndicator size="large" />
        <Text>Requesting camera permission...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={[commonStyles.centerContainer, { backgroundColor: paperTheme.colors.background }]}>
        <Card style={[styles.permissionCard, commonStyles.shadow]}>
          <Card.Content style={{ alignItems: 'center' }}>
            <Text variant="headlineSmall" style={styles.permissionTitle}>
              Camera Access Required
            </Text>
            <Text variant="bodyLarge" style={styles.permissionText}>
              Apex needs camera access to identify objects and scan barcodes.
            </Text>
            <Button
              mode="contained"
              onPress={requestPermission}
              style={[commonStyles.button, { marginTop: 16 }]}
            >
              Grant Permission
            </Button>
          </Card.Content>
        </Card>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Camera View */}
      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={facing}
          flash={flash ? 'on' : 'off'}
        >
          {/* Top Controls */}
          <View style={styles.topControls}>
            <IconButton
              icon={flash ? 'flash' : 'flash-off'}
              iconColor="white"
              size={24}
              onPress={toggleFlash}
              style={styles.controlButton}
            />
            
            <View style={styles.statusContainer}>
              {!isOnline && (
                <Chip
                  icon="wifi-off"
                  textStyle={{ color: 'white' }}
                  style={styles.offlineChip}
                >
                  Offline
                </Chip>
              )}
              {categoryFilter && (
                <Chip
                  icon="filter"
                  textStyle={{ color: 'white' }}
                  style={styles.filterChip}
                  onPress={() => setShowCategoryModal(true)}
                >
                  {categories.find(c => c.key === categoryFilter)?.label}
                </Chip>
              )}
            </View>

            <IconButton
              icon="camera-flip"
              iconColor="white"
              size={24}
              onPress={toggleCamera}
              style={styles.controlButton}
            />
          </View>

          {/* Center Overlay */}
          <View style={styles.centerOverlay}>
            <View style={styles.scanFrame} />
            {isProcessing && (
              <View style={styles.processingOverlay}>
                <ActivityIndicator size="large" color="white" />
                <Text style={styles.processingText}>
                  Analyzing with AI...
                </Text>
              </View>
            )}
          </View>

          {/* Bottom Controls */}
          <View style={styles.bottomControls}>
            <IconButton
              icon="image"
              iconColor="white"
              size={32}
              onPress={handleGalleryPick}
              disabled={isProcessing || isCapturing}
              style={styles.galleryButton}
            />

            <View style={styles.captureButtonContainer}>
              <IconButton
                icon="camera"
                iconColor={paperTheme.colors.primary}
                size={40}
                onPress={handleCapture}
                disabled={isProcessing || isCapturing}
                style={[
                  styles.captureButton,
                  { backgroundColor: 'white' },
                  (isProcessing || isCapturing) && styles.captureButtonDisabled,
                ]}
              />
            </View>

            <IconButton
              icon="tune"
              iconColor="white"
              size={32}
              onPress={() => setShowCategoryModal(true)}
              style={styles.settingsButton}
            />
          </View>
        </CameraView>
      </View>

      {/* Category Filter Modal */}
      <Portal>
        <Modal
          visible={showCategoryModal}
          onDismiss={() => setShowCategoryModal(false)}
          contentContainerStyle={[
            styles.modalContainer,
            { backgroundColor: paperTheme.colors.surface }
          ]}
        >
          <Text variant="headlineSmall" style={styles.modalTitle}>
            Filter by Category
          </Text>
          
          <List.Item
            title="All Categories"
            left={() => <List.Icon icon="all-inclusive" />}
            onPress={() => {
              setCategoryFilter(null);
              setShowCategoryModal(false);
            }}
            style={!categoryFilter && styles.selectedCategory}
          />
          
          {categories.map((category) => (
            <List.Item
              key={category.key}
              title={category.label}
              left={() => <Text style={styles.categoryEmoji}>{category.icon}</Text>}
              onPress={() => {
                setCategoryFilter(category.key as CategoryFilter);
                setShowCategoryModal(false);
              }}
              style={categoryFilter === category.key && styles.selectedCategory}
            />
          ))}
        </Modal>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingTop: 20,
    paddingHorizontal: 20,
  },
  controlButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  statusContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  offlineChip: {
    backgroundColor: 'rgba(255,152,0,0.8)',
  },
  filterChip: {
    backgroundColor: 'rgba(76,175,80,0.8)',
  },
  centerOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  processingOverlay: {
    position: 'absolute',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 20,
    borderRadius: 12,
  },
  processingText: {
    color: 'white',
    marginTop: 12,
    fontSize: 16,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 40,
    paddingHorizontal: 40,
  },
  galleryButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  captureButtonContainer: {
    alignItems: 'center',
  },
  captureButton: {
    borderWidth: 4,
    borderColor: 'white',
  },
  captureButtonDisabled: {
    opacity: 0.5,
  },
  settingsButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  permissionCard: {
    margin: 20,
    maxWidth: 400,
  },
  permissionTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  permissionText: {
    textAlign: 'center',
    opacity: 0.7,
  },
  modalContainer: {
    margin: 20,
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%',
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  selectedCategory: {
    backgroundColor: 'rgba(76,175,80,0.1)',
  },
  categoryEmoji: {
    fontSize: 24,
    textAlign: 'center',
    width: 40,
  },
});
