import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
    Dimensions,
    FlatList,
    Image,
    RefreshControl,
    StyleSheet,
    View,
} from 'react-native';
import {
    Button,
    Card,
    Chip,
    FAB,
    IconButton,
    List,
    Modal,
    Portal,
    Searchbar,
    Snackbar,
    Text
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAuthStore } from '../stores/authStoreFirebase';
import { useScanStore } from '../stores/scanStoreTurso';
import { commonStyles } from '../theme/theme';
import { useTheme } from '../theme/ThemeProvider';

const { width: screenWidth } = Dimensions.get('window');
const ITEM_WIDTH = (screenWidth - 48) / 2; // 2 columns with padding

type ViewMode = 'grid' | 'list';
type FilterMode = 'all' | 'favorites' | 'category';

export default function HistoryScreen() {
  const { paperTheme } = useTheme();
  const { user } = useAuthStore();
  const {
    scans,
    favorites,
    searchQuery,
    categoryFilter,
    setSearchQuery,
    setCategoryFilter,
    getFilteredScans,
    loadScans,
    searchScans,
    toggleFavorite,
    isOnline,
    isSyncing,
    lastSyncTime,
    forcSync,
  } = useScanStore();

  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [filterMode, setFilterMode] = useState<FilterMode>('all');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const categories = [
    { key: 'food', label: 'Food', icon: '🍎' },
    { key: 'plant', label: 'Plants', icon: '🌱' },
    { key: 'animal', label: 'Animals', icon: '🦋' },
    { key: 'rock', label: 'Rocks & Minerals', icon: '💎' },
    { key: 'coin', label: 'Coins', icon: '🪙' },
    { key: 'insect', label: 'Insects', icon: '🐛' },
  ];

  useEffect(() => {
    if (user) {
      loadScans(user.id);
    }
  }, [user]);

  const handleRefresh = useCallback(async () => {
    if (!user) return;
    
    setRefreshing(true);
    try {
      if (isOnline) {
        await forcSync();
      }
      await loadScans(user.id);
    } catch (error) {
      setSnackbarMessage('Failed to refresh data');
      setSnackbarVisible(true);
    } finally {
      setRefreshing(false);
    }
  }, [user, isOnline, forcSync, loadScans]);

  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query);
    if (user && query.trim()) {
      try {
        await searchScans(user.id, query, categoryFilter);
      } catch (error) {
        console.error('Search error:', error);
      }
    } else if (user) {
      await loadScans(user.id);
    }
  }, [user, categoryFilter, setSearchQuery, searchScans, loadScans]);

  const handleCategoryFilter = (category: string | null) => {
    setCategoryFilter(category);
    setShowFilterModal(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleFavoriteToggle = async (scanId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      await toggleFavorite(scanId);
    } catch (error) {
      setSnackbarMessage('Failed to update favorite');
      setSnackbarVisible(true);
    }
  };

  const getDisplayData = () => {
    switch (filterMode) {
      case 'favorites':
        return favorites;
      case 'category':
        return getFilteredScans();
      default:
        return searchQuery ? getFilteredScans() : scans;
    }
  };

  const renderScanItem = ({ item }: { item: any }) => {
    const isGrid = viewMode === 'grid';
    
    return (
      <Card
        style={[
          styles.scanCard,
          isGrid ? styles.gridCard : styles.listCard,
          commonStyles.shadow,
        ]}
        onPress={() => router.push({
          pathname: '/(tabs)/scan-results',
          params: { scanId: item.id },
        })}
      >
        <View style={isGrid ? styles.gridContent : styles.listContent}>
          <Image
            source={{ uri: item.image_uri }}
            style={isGrid ? styles.gridImage : styles.listImage}
          />
          
          <View style={isGrid ? styles.gridInfo : styles.listInfo}>
            <Text
              variant={isGrid ? 'titleMedium' : 'titleLarge'}
              numberOfLines={2}
              style={styles.itemName}
            >
              {item.identification.name}
            </Text>
            
            {item.identification.scientific_name && (
              <Text
                variant="bodySmall"
                numberOfLines={1}
                style={styles.scientificName}
              >
                {item.identification.scientific_name}
              </Text>
            )}
            
            <View style={styles.itemMetadata}>
              <Chip
                textStyle={{ fontSize: 10 }}
                style={styles.categoryChip}
              >
                {categories.find(c => c.key === item.category)?.icon} {item.category}
              </Chip>
              
              <Text variant="bodySmall" style={styles.dateText}>
                {new Date(item.metadata.timestamp).toLocaleDateString()}
              </Text>
            </View>
            
            <View style={styles.itemActions}>
              <Text
                variant="bodySmall"
                style={[
                  styles.confidenceText,
                  { color: item.identification.confidence >= 0.8 ? paperTheme.colors.success : paperTheme.colors.warning }
                ]}
              >
                {Math.round(item.identification.confidence * 100)}% confident
              </Text>
              
              <IconButton
                icon={item.is_favorite ? 'heart' : 'heart-outline'}
                iconColor={item.is_favorite ? '#FF6B6B' : paperTheme.colors.outline}
                size={20}
                onPress={() => handleFavoriteToggle(item.id)}
                style={styles.favoriteButton}
              />
            </View>
          </View>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={commonStyles.centerContainer}>
      <Text variant="headlineSmall" style={styles.emptyTitle}>
        {filterMode === 'favorites' ? 'No Favorites Yet' : 'No Scans Yet'}
      </Text>
      <Text variant="bodyLarge" style={styles.emptySubtitle}>
        {filterMode === 'favorites' 
          ? 'Heart your favorite discoveries to see them here'
          : 'Start scanning objects to build your collection'
        }
      </Text>
      <Button
        mode="contained"
        onPress={() => router.push('/(tabs)/camera')}
        style={styles.emptyButton}
        icon="camera"
      >
        Start Scanning
      </Button>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <Searchbar
        placeholder="Search your scans..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchBar}
        icon="magnify"
        clearIcon="close"
      />
      
      {/* Filter Controls */}
      <View style={styles.filterControls}>
        <View style={styles.filterTabs}>
          <Chip
            selected={filterMode === 'all'}
            onPress={() => setFilterMode('all')}
            style={styles.filterChip}
          >
            All ({scans.length})
          </Chip>
          <Chip
            selected={filterMode === 'favorites'}
            onPress={() => setFilterMode('favorites')}
            style={styles.filterChip}
          >
            ❤️ Favorites ({favorites.length})
          </Chip>
        </View>
        
        <View style={styles.viewControls}>
          <IconButton
            icon="filter"
            onPress={() => setShowFilterModal(true)}
            style={[
              styles.controlButton,
              categoryFilter && { backgroundColor: paperTheme.colors.primaryContainer }
            ]}
          />
          <IconButton
            icon={viewMode === 'grid' ? 'view-list' : 'view-grid'}
            onPress={() => {
              setViewMode(viewMode === 'grid' ? 'list' : 'grid');
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }}
            style={styles.controlButton}
          />
        </View>
      </View>
      
      {/* Active Filters */}
      {categoryFilter && (
        <View style={styles.activeFilters}>
          <Chip
            icon="filter"
            onClose={() => handleCategoryFilter(null)}
            style={styles.activeFilterChip}
          >
            {categories.find(c => c.key === categoryFilter)?.label}
          </Chip>
        </View>
      )}
      
      {/* Sync Status */}
      <View style={styles.syncStatus}>
        <View style={commonStyles.row}>
          <View style={[
            styles.syncIndicator,
            { backgroundColor: isOnline ? paperTheme.colors.success : paperTheme.colors.outline }
          ]} />
          <Text variant="bodySmall" style={styles.syncText}>
            {isOnline ? 'Online' : 'Offline'}
            {isSyncing && ' • Syncing...'}
            {lastSyncTime && !isSyncing && ` • Last sync: ${new Date(lastSyncTime).toLocaleTimeString()}`}
          </Text>
        </View>
        
        {isOnline && (
          <IconButton
            icon="refresh"
            size={16}
            onPress={handleRefresh}
            disabled={refreshing || isSyncing}
          />
        )}
      </View>
    </View>
  );

  const displayData = getDisplayData();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: paperTheme.colors.background }]}>
      <FlatList
        data={displayData}
        renderItem={renderScanItem}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={displayData.length === 0 ? styles.emptyContainer : styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[paperTheme.colors.primary]}
          />
        }
      />

      {/* Floating Action Button */}
      <FAB
        icon="camera"
        style={[styles.fab, { backgroundColor: paperTheme.colors.primary }]}
        onPress={() => router.push('/(tabs)/camera')}
      />

      {/* Filter Modal */}
      <Portal>
        <Modal
          visible={showFilterModal}
          onDismiss={() => setShowFilterModal(false)}
          contentContainerStyle={[
            styles.modalContainer,
            { backgroundColor: paperTheme.colors.surface }
          ]}
        >
          <Text variant="headlineSmall" style={styles.modalTitle}>
            Filter by Category
          </Text>
          
          <List.Item
            title="All Categories"
            left={() => <List.Icon icon="all-inclusive" />}
            onPress={() => handleCategoryFilter(null)}
            style={!categoryFilter && styles.selectedCategory}
          />
          
          {categories.map((category) => (
            <List.Item
              key={category.key}
              title={category.label}
              left={() => <Text style={styles.categoryEmoji}>{category.icon}</Text>}
              onPress={() => handleCategoryFilter(category.key)}
              style={categoryFilter === category.key && styles.selectedCategory}
            />
          ))}
        </Modal>
      </Portal>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    marginBottom: 16,
  },
  filterControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTabs: {
    flexDirection: 'row',
    gap: 8,
  },
  filterChip: {
    height: 32,
  },
  viewControls: {
    flexDirection: 'row',
  },
  controlButton: {
    margin: 0,
  },
  activeFilters: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  activeFilterChip: {
    marginRight: 8,
  },
  syncStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  syncIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  syncText: {
    opacity: 0.7,
  },
  listContainer: {
    paddingBottom: 80,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  scanCard: {
    margin: 4,
  },
  gridCard: {
    width: ITEM_WIDTH,
  },
  listCard: {
    marginHorizontal: 12,
    marginVertical: 4,
  },
  gridContent: {
    padding: 8,
  },
  listContent: {
    flexDirection: 'row',
    padding: 12,
  },
  gridImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  listImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  gridInfo: {
    flex: 1,
  },
  listInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  itemName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  scientificName: {
    fontStyle: 'italic',
    opacity: 0.7,
    marginBottom: 8,
  },
  itemMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryChip: {
    height: 24,
  },
  dateText: {
    opacity: 0.7,
  },
  itemActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  favoriteButton: {
    margin: 0,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 24,
  },
  emptyButton: {
    alignSelf: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    margin: 20,
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%',
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  selectedCategory: {
    backgroundColor: 'rgba(76,175,80,0.1)',
  },
  categoryEmoji: {
    fontSize: 24,
    textAlign: 'center',
    width: 40,
  },
});
