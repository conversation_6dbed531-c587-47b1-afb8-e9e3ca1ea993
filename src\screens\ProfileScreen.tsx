import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Share,
} from 'react-native';
import {
  Text,
  Card,
  List,
  Avatar,
  Button,
  Divider,
  Switch,
  Portal,
  Modal,
  RadioButton,
  IconButton,
  Chip,
  ProgressBar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { useTheme, ThemeVariant } from '../theme/ThemeProvider';
import { useAuthStore } from '../stores/authStoreFirebase';
import { useScanStore } from '../stores/scanStoreTurso';
import { syncService } from '../services/syncService';
import { commonStyles } from '../theme/theme';

export default function ProfileScreen() {
  const { paperTheme, currentTheme, setTheme } = useTheme();
  const { user, signOut, updateProfile, isLoading } = useAuthStore();
  const { scans, favorites, isOnline, isSyncing, lastSyncTime, forcSync } = useScanStore();
  
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [showSyncModal, setShowSyncModal] = useState(false);

  const themeOptions: { key: ThemeVariant; label: string; description: string }[] = [
    { key: 'light', label: 'Light', description: 'Clean and bright interface' },
    { key: 'dark', label: 'Dark', description: 'Easy on the eyes' },
    { key: 'nature', label: 'Nature', description: 'Forest green theme' },
    { key: 'ocean', label: 'Ocean', description: 'Deep blue theme' },
    { key: 'sunset', label: 'Sunset', description: 'Warm orange theme' },
  ];

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            await signOut();
            router.replace('/auth');
          },
        },
      ]
    );
  };

  const handleThemeChange = async (theme: ThemeVariant) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    await setTheme(theme);
    setShowThemeModal(false);
  };

  const handleNotificationToggle = async (enabled: boolean) => {
    if (!user) return;
    
    try {
      await updateProfile({
        preferences: {
          ...user.preferences,
          notifications: enabled,
        },
      });
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      Alert.alert('Error', 'Failed to update notification settings');
    }
  };

  const handleLocationToggle = async (enabled: boolean) => {
    if (!user) return;
    
    try {
      await updateProfile({
        preferences: {
          ...user.preferences,
          location_sharing: enabled,
        },
      });
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      Alert.alert('Error', 'Failed to update location settings');
    }
  };

  const handleForceSync = async () => {
    if (!isOnline) {
      Alert.alert('Offline', 'Cannot sync while offline');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      await forcSync();
      setShowSyncModal(false);
      Alert.alert('Success', 'Data synchronized successfully');
    } catch (error) {
      Alert.alert('Sync Failed', 'Failed to synchronize data. Please try again.');
    }
  };

  const handleShareApp = async () => {
    try {
      await Share.share({
        message: 'Check out Apex - the AI-powered object identification app! Discover and learn about the world around you.',
        url: 'https://apex-bioscan.com', // Replace with actual app store URL
      });
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const getSubscriptionBadge = () => {
    if (user?.subscription_tier === 'premium') {
      return (
        <Chip
          icon="crown"
          textStyle={{ color: '#FFD700' }}
          style={{ backgroundColor: 'rgba(255, 215, 0, 0.1)' }}
        >
          Premium
        </Chip>
      );
    }
    return (
      <Chip
        icon="account"
        style={{ backgroundColor: paperTheme.colors.surfaceVariant }}
      >
        Free
      </Chip>
    );
  };

  const getSyncStatus = () => {
    if (isSyncing) return 'Syncing...';
    if (!isOnline) return 'Offline';
    if (lastSyncTime) {
      const timeDiff = Date.now() - new Date(lastSyncTime).getTime();
      const minutes = Math.floor(timeDiff / 60000);
      if (minutes < 1) return 'Just synced';
      if (minutes < 60) return `${minutes}m ago`;
      const hours = Math.floor(minutes / 60);
      return `${hours}h ago`;
    }
    return 'Never synced';
  };

  if (!user) {
    return (
      <SafeAreaView style={[commonStyles.centerContainer, { backgroundColor: paperTheme.colors.background }]}>
        <Text variant="headlineSmall">Please sign in</Text>
        <Button
          mode="contained"
          onPress={() => router.replace('/auth')}
          style={{ marginTop: 16 }}
        >
          Sign In
        </Button>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: paperTheme.colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Card style={[styles.headerCard, commonStyles.shadow]}>
          <Card.Content style={styles.headerContent}>
            <Avatar.Text
              size={80}
              label={user.displayName ? user.displayName.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
              style={{ backgroundColor: paperTheme.colors.primary }}
            />
            
            <View style={styles.userInfo}>
              <Text variant="headlineSmall" style={styles.userName}>
                {user.displayName || 'User'}
              </Text>
              <Text variant="bodyMedium" style={styles.userEmail}>
                {user.email}
              </Text>
              <View style={styles.userBadges}>
                {getSubscriptionBadge()}
                <Chip
                  icon={user.emailVerified ? 'check-circle' : 'alert-circle'}
                  textStyle={{ 
                    color: user.emailVerified ? paperTheme.colors.success : paperTheme.colors.warning 
                  }}
                  style={{ 
                    backgroundColor: user.emailVerified 
                      ? 'rgba(76, 175, 80, 0.1)' 
                      : 'rgba(255, 152, 0, 0.1)' 
                  }}
                >
                  {user.emailVerified ? 'Verified' : 'Unverified'}
                </Chip>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Statistics */}
        <Card style={[styles.card, commonStyles.shadow]}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.sectionTitle}>Statistics</Text>
            <Divider style={styles.divider} />
            
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text variant="headlineMedium" style={[styles.statNumber, { color: paperTheme.colors.primary }]}>
                  {scans.length}
                </Text>
                <Text variant="bodyMedium" style={styles.statLabel}>Total Scans</Text>
              </View>
              
              <View style={styles.statItem}>
                <Text variant="headlineMedium" style={[styles.statNumber, { color: '#FF6B6B' }]}>
                  {favorites.length}
                </Text>
                <Text variant="bodyMedium" style={styles.statLabel}>Favorites</Text>
              </View>
              
              <View style={styles.statItem}>
                <Text variant="headlineMedium" style={[styles.statNumber, { color: paperTheme.colors.secondary }]}>
                  {new Set(scans.map(s => s.category)).size}
                </Text>
                <Text variant="bodyMedium" style={styles.statLabel}>Categories</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Sync Status */}
        <Card style={[styles.card, commonStyles.shadow]}>
          <Card.Content>
            <View style={commonStyles.spaceBetween}>
              <Text variant="titleLarge" style={styles.sectionTitle}>Sync Status</Text>
              <IconButton
                icon="information"
                onPress={() => setShowSyncModal(true)}
              />
            </View>
            <Divider style={styles.divider} />
            
            <View style={styles.syncStatusContainer}>
              <View style={commonStyles.row}>
                <View style={[
                  styles.syncIndicator,
                  { backgroundColor: isOnline ? paperTheme.colors.success : paperTheme.colors.outline }
                ]} />
                <Text variant="bodyLarge">
                  {isOnline ? 'Online' : 'Offline'} • {getSyncStatus()}
                </Text>
              </View>
              
              {isSyncing && (
                <ProgressBar
                  indeterminate
                  color={paperTheme.colors.primary}
                  style={styles.syncProgress}
                />
              )}
              
              <Button
                mode="outlined"
                onPress={handleForceSync}
                disabled={!isOnline || isSyncing}
                style={styles.syncButton}
                icon="sync"
              >
                Force Sync
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Settings */}
        <Card style={[styles.card, commonStyles.shadow]}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.sectionTitle}>Settings</Text>
            <Divider style={styles.divider} />
            
            <List.Item
              title="Theme"
              description={themeOptions.find(t => t.key === currentTheme)?.label}
              left={() => <List.Icon icon="palette" />}
              right={() => <List.Icon icon="chevron-right" />}
              onPress={() => setShowThemeModal(true)}
            />
            
            <List.Item
              title="Push Notifications"
              description="Get notified about sync updates and tips"
              left={() => <List.Icon icon="bell" />}
              right={() => (
                <Switch
                  value={user.preferences?.notifications ?? true}
                  onValueChange={handleNotificationToggle}
                  disabled={isLoading}
                />
              )}
            />
            
            <List.Item
              title="Location Sharing"
              description="Save location data with scans"
              left={() => <List.Icon icon="map-marker" />}
              right={() => (
                <Switch
                  value={user.preferences?.location_sharing ?? false}
                  onValueChange={handleLocationToggle}
                  disabled={isLoading}
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* Actions */}
        <Card style={[styles.card, commonStyles.shadow]}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.sectionTitle}>More</Text>
            <Divider style={styles.divider} />
            
            <List.Item
              title="Share Apex"
              description="Tell friends about the app"
              left={() => <List.Icon icon="share" />}
              right={() => <List.Icon icon="chevron-right" />}
              onPress={handleShareApp}
            />
            
            <List.Item
              title="Help & Support"
              description="Get help and contact support"
              left={() => <List.Icon icon="help-circle" />}
              right={() => <List.Icon icon="chevron-right" />}
              onPress={() => {
                // TODO: Navigate to help screen
                Alert.alert('Help', 'Help & Support coming soon!');
              }}
            />
            
            <List.Item
              title="Privacy Policy"
              description="Read our privacy policy"
              left={() => <List.Icon icon="shield-account" />}
              right={() => <List.Icon icon="chevron-right" />}
              onPress={() => {
                // TODO: Open privacy policy
                Alert.alert('Privacy', 'Privacy Policy coming soon!');
              }}
            />
          </Card.Content>
        </Card>

        {/* Sign Out */}
        <Button
          mode="outlined"
          onPress={handleSignOut}
          style={[styles.signOutButton, { borderColor: paperTheme.colors.error }]}
          textColor={paperTheme.colors.error}
          icon="logout"
          disabled={isLoading}
        >
          Sign Out
        </Button>
      </ScrollView>

      {/* Theme Modal */}
      <Portal>
        <Modal
          visible={showThemeModal}
          onDismiss={() => setShowThemeModal(false)}
          contentContainerStyle={[
            styles.modalContainer,
            { backgroundColor: paperTheme.colors.surface }
          ]}
        >
          <Text variant="headlineSmall" style={styles.modalTitle}>
            Choose Theme
          </Text>
          
          <RadioButton.Group
            onValueChange={(value) => handleThemeChange(value as ThemeVariant)}
            value={currentTheme}
          >
            {themeOptions.map((theme) => (
              <RadioButton.Item
                key={theme.key}
                label={theme.label}
                value={theme.key}
                style={styles.radioItem}
              />
            ))}
          </RadioButton.Group>
        </Modal>
      </Portal>

      {/* Sync Info Modal */}
      <Portal>
        <Modal
          visible={showSyncModal}
          onDismiss={() => setShowSyncModal(false)}
          contentContainerStyle={[
            styles.modalContainer,
            { backgroundColor: paperTheme.colors.surface }
          ]}
        >
          <Text variant="headlineSmall" style={styles.modalTitle}>
            Sync Information
          </Text>
          
          <Text variant="bodyMedium" style={styles.syncInfo}>
            Apex uses a hybrid sync system:
          </Text>
          
          <Text variant="bodyMedium" style={styles.syncInfo}>
            • <Text style={{ fontWeight: 'bold' }}>Local Storage:</Text> All data is stored locally on your device using Turso SQLite for offline access.
          </Text>
          
          <Text variant="bodyMedium" style={styles.syncInfo}>
            • <Text style={{ fontWeight: 'bold' }}>Cloud Backup:</Text> When online, data syncs to Supabase cloud database for backup and cross-device access.
          </Text>
          
          <Text variant="bodyMedium" style={styles.syncInfo}>
            • <Text style={{ fontWeight: 'bold' }}>Automatic Sync:</Text> Happens in the background every 30 seconds when online.
          </Text>
          
          <Button
            mode="contained"
            onPress={() => setShowSyncModal(false)}
            style={styles.modalButton}
          >
            Got it
          </Button>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userInfo: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontWeight: 'bold',
  },
  userEmail: {
    opacity: 0.7,
    marginBottom: 8,
  },
  userBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  card: {
    margin: 16,
    marginTop: 8,
  },
  sectionTitle: {
    fontWeight: 'bold',
  },
  divider: {
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontWeight: 'bold',
  },
  statLabel: {
    opacity: 0.7,
    marginTop: 4,
  },
  syncStatusContainer: {
    gap: 12,
  },
  syncIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  syncProgress: {
    height: 4,
    borderRadius: 2,
  },
  syncButton: {
    alignSelf: 'flex-start',
  },
  signOutButton: {
    margin: 16,
    marginTop: 8,
    marginBottom: 32,
  },
  modalContainer: {
    margin: 20,
    borderRadius: 12,
    padding: 20,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  radioItem: {
    paddingVertical: 4,
  },
  syncInfo: {
    marginBottom: 12,
    lineHeight: 20,
  },
  modalButton: {
    marginTop: 16,
  },
});
