import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
  Dimensions,
  Share,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  IconButton,
  Chip,
  Divider,
  TextInput,
  Portal,
  Modal,
  List,
  ProgressBar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { useTheme } from '../theme/ThemeProvider';
import { useScanStore } from '../stores/scanStoreTurso';
import { commonStyles } from '../theme/theme';

const { width: screenWidth } = Dimensions.get('window');

export default function ScanResultsScreen() {
  const { paperTheme } = useTheme();
  const { scanId } = useLocalSearchParams<{ scanId: string }>();
  const { scans, updateScan, toggleFavorite } = useScanStore();
  
  const [scan, setScan] = useState(null);
  const [userNotes, setUserNotes] = useState('');
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [showFactSheet, setShowFactSheet] = useState(true);

  useEffect(() => {
    if (scanId) {
      const foundScan = scans.find(s => s.id === scanId);
      if (foundScan) {
        setScan(foundScan);
        setUserNotes(foundScan.user_notes || '');
      }
    }
  }, [scanId, scans]);

  if (!scan) {
    return (
      <SafeAreaView style={[commonStyles.centerContainer, { backgroundColor: paperTheme.colors.background }]}>
        <Text>Scan not found</Text>
        <Button onPress={() => router.back()}>Go Back</Button>
      </SafeAreaView>
    );
  }

  const handleFavorite = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    await toggleFavorite(scan.id);
  };

  const handleShare = async () => {
    try {
      const shareContent = {
        message: `Check out this ${scan.category} I identified with Apex: ${scan.identification.name}${scan.identification.scientific_name ? ` (${scan.identification.scientific_name})` : ''}`,
        url: scan.image_uri,
      };
      
      await Share.share(shareContent);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const handleSaveNotes = async () => {
    try {
      await updateScan(scan.id, { user_notes: userNotes });
      setShowNotesModal(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Save notes error:', error);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return paperTheme.colors.success;
    if (confidence >= 0.6) return paperTheme.colors.warning;
    return paperTheme.colors.error;
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      food: '🍎',
      plant: '🌱',
      animal: '🦋',
      rock: '💎',
      coin: '🪙',
      insect: '🐛',
    };
    return icons[category] || '🔍';
  };

  const renderFactSheet = () => {
    if (!scan.fact_sheet) return null;

    const { description, key_features, interesting_facts, category_specific } = scan.fact_sheet;

    return (
      <Card style={[styles.card, commonStyles.shadow]}>
        <Card.Content>
          <View style={commonStyles.spaceBetween}>
            <Text variant="headlineSmall">Fact Sheet</Text>
            <IconButton
              icon={showFactSheet ? 'chevron-up' : 'chevron-down'}
              onPress={() => setShowFactSheet(!showFactSheet)}
            />
          </View>
          
          {showFactSheet && (
            <>
              <Divider style={styles.divider} />
              
              {description && (
                <View style={styles.section}>
                  <Text variant="titleMedium" style={styles.sectionTitle}>Description</Text>
                  <Text variant="bodyMedium">{description}</Text>
                </View>
              )}

              {key_features && key_features.length > 0 && (
                <View style={styles.section}>
                  <Text variant="titleMedium" style={styles.sectionTitle}>Key Features</Text>
                  {key_features.map((feature, index) => (
                    <Text key={index} variant="bodyMedium" style={styles.bulletPoint}>
                      • {feature}
                    </Text>
                  ))}
                </View>
              )}

              {interesting_facts && interesting_facts.length > 0 && (
                <View style={styles.section}>
                  <Text variant="titleMedium" style={styles.sectionTitle}>Did You Know?</Text>
                  {interesting_facts.map((fact, index) => (
                    <Text key={index} variant="bodyMedium" style={styles.bulletPoint}>
                      💡 {fact}
                    </Text>
                  ))}
                </View>
              )}

              {category_specific && (
                <View style={styles.section}>
                  <Text variant="titleMedium" style={styles.sectionTitle}>
                    {scan.category.charAt(0).toUpperCase() + scan.category.slice(1)} Specific Info
                  </Text>
                  {Object.entries(category_specific).map(([key, value]) => (
                    <View key={key} style={styles.infoRow}>
                      <Text variant="bodyMedium" style={styles.infoLabel}>
                        {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                      </Text>
                      <Text variant="bodyMedium" style={styles.infoValue}>
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </>
          )}
        </Card.Content>
      </Card>
    );
  };

  const renderAlternatives = () => {
    if (!scan.identification.alternatives || scan.identification.alternatives.length === 0) {
      return null;
    }

    return (
      <Card style={[styles.card, commonStyles.shadow]}>
        <Card.Content>
          <Text variant="titleLarge" style={styles.sectionTitle}>Alternative Identifications</Text>
          <Divider style={styles.divider} />
          
          {scan.identification.alternatives.map((alt, index) => (
            <View key={index} style={styles.alternativeItem}>
              <View style={styles.alternativeHeader}>
                <Text variant="titleMedium">{alt.name}</Text>
                <Chip
                  textStyle={{ fontSize: 12 }}
                  style={[styles.confidenceChip, { backgroundColor: getConfidenceColor(alt.confidence) }]}
                >
                  {Math.round(alt.confidence * 100)}%
                </Chip>
              </View>
              {alt.scientific_name && (
                <Text variant="bodySmall" style={styles.scientificName}>
                  {alt.scientific_name}
                </Text>
              )}
            </View>
          ))}
        </Card.Content>
      </Card>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: paperTheme.colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header with Image */}
        <View style={styles.header}>
          <Image source={{ uri: scan.image_uri }} style={styles.image} />
          <View style={styles.imageOverlay}>
            <IconButton
              icon="arrow-left"
              iconColor="white"
              size={24}
              onPress={() => router.back()}
              style={styles.backButton}
            />
            <View style={styles.headerActions}>
              <IconButton
                icon={scan.is_favorite ? 'heart' : 'heart-outline'}
                iconColor={scan.is_favorite ? '#FF6B6B' : 'white'}
                size={24}
                onPress={handleFavorite}
                style={styles.actionButton}
              />
              <IconButton
                icon="share"
                iconColor="white"
                size={24}
                onPress={handleShare}
                style={styles.actionButton}
              />
            </View>
          </View>
        </View>

        <View style={styles.content}>
          {/* Main Identification */}
          <Card style={[styles.card, commonStyles.shadow]}>
            <Card.Content>
              <View style={styles.identificationHeader}>
                <Text style={styles.categoryEmoji}>{getCategoryIcon(scan.category)}</Text>
                <View style={styles.identificationInfo}>
                  <Text variant="headlineMedium" style={styles.itemName}>
                    {scan.identification.name}
                  </Text>
                  {scan.identification.scientific_name && (
                    <Text variant="bodyLarge" style={styles.scientificName}>
                      {scan.identification.scientific_name}
                    </Text>
                  )}
                </View>
              </View>

              <View style={styles.metadataRow}>
                <Chip
                  icon="tag"
                  style={[styles.categoryChip, { backgroundColor: paperTheme.colors.primaryContainer }]}
                >
                  {scan.category.charAt(0).toUpperCase() + scan.category.slice(1)}
                </Chip>
                
                <View style={styles.confidenceContainer}>
                  <Text variant="bodySmall" style={styles.confidenceLabel}>Confidence</Text>
                  <ProgressBar
                    progress={scan.identification.confidence}
                    color={getConfidenceColor(scan.identification.confidence)}
                    style={styles.confidenceBar}
                  />
                  <Text variant="bodySmall" style={styles.confidenceText}>
                    {Math.round(scan.identification.confidence * 100)}%
                  </Text>
                </View>
              </View>

              {/* Scan Metadata */}
              <Divider style={styles.divider} />
              <View style={styles.metadataSection}>
                <Text variant="bodySmall" style={styles.metadataText}>
                  Scanned {new Date(scan.metadata.timestamp).toLocaleDateString()} via {scan.metadata.scan_method}
                </Text>
                {scan.location && (
                  <Text variant="bodySmall" style={styles.metadataText}>
                    📍 Location: {scan.location.latitude.toFixed(4)}, {scan.location.longitude.toFixed(4)}
                  </Text>
                )}
              </View>
            </Card.Content>
          </Card>

          {/* User Notes */}
          <Card style={[styles.card, commonStyles.shadow]}>
            <Card.Content>
              <View style={commonStyles.spaceBetween}>
                <Text variant="titleLarge">My Notes</Text>
                <IconButton
                  icon="pencil"
                  onPress={() => setShowNotesModal(true)}
                />
              </View>
              <Text variant="bodyMedium" style={styles.notesText}>
                {scan.user_notes || 'No notes added yet. Tap the pencil to add your observations!'}
              </Text>
            </Card.Content>
          </Card>

          {/* Fact Sheet */}
          {renderFactSheet()}

          {/* Alternative Identifications */}
          {renderAlternatives()}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={() => router.push('/(tabs)/camera')}
              style={styles.actionButtonStyle}
              icon="camera"
            >
              Scan Another
            </Button>
            <Button
              mode="contained"
              onPress={() => router.push('/(tabs)/history')}
              style={styles.actionButtonStyle}
              icon="history"
            >
              View History
            </Button>
          </View>
        </View>
      </ScrollView>

      {/* Notes Modal */}
      <Portal>
        <Modal
          visible={showNotesModal}
          onDismiss={() => setShowNotesModal(false)}
          contentContainerStyle={[
            styles.modalContainer,
            { backgroundColor: paperTheme.colors.surface }
          ]}
        >
          <Text variant="headlineSmall" style={styles.modalTitle}>
            Add Notes
          </Text>
          
          <TextInput
            label="Your observations and notes"
            value={userNotes}
            onChangeText={setUserNotes}
            mode="outlined"
            multiline
            numberOfLines={4}
            style={styles.notesInput}
          />
          
          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setShowNotesModal(false)}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveNotes}
              style={styles.modalButton}
            >
              Save
            </Button>
          </View>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    position: 'relative',
  },
  image: {
    width: screenWidth,
    height: 300,
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  backButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    marginLeft: 8,
  },
  content: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  identificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryEmoji: {
    fontSize: 48,
    marginRight: 16,
  },
  identificationInfo: {
    flex: 1,
  },
  itemName: {
    fontWeight: 'bold',
  },
  scientificName: {
    fontStyle: 'italic',
    opacity: 0.7,
    marginTop: 4,
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryChip: {
    alignSelf: 'flex-start',
  },
  confidenceContainer: {
    alignItems: 'flex-end',
    flex: 1,
    marginLeft: 16,
  },
  confidenceLabel: {
    marginBottom: 4,
  },
  confidenceBar: {
    width: 100,
    height: 8,
    borderRadius: 4,
  },
  confidenceText: {
    marginTop: 4,
    fontWeight: 'bold',
  },
  divider: {
    marginVertical: 16,
  },
  metadataSection: {
    marginTop: 8,
  },
  metadataText: {
    opacity: 0.7,
    marginBottom: 4,
  },
  notesText: {
    marginTop: 8,
    fontStyle: scan => scan.user_notes ? 'normal' : 'italic',
    opacity: scan => scan.user_notes ? 1 : 0.7,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 8,
    fontWeight: 'bold',
  },
  bulletPoint: {
    marginBottom: 4,
    marginLeft: 8,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  infoLabel: {
    fontWeight: 'bold',
    marginRight: 8,
    flex: 1,
  },
  infoValue: {
    flex: 2,
  },
  alternativeItem: {
    marginBottom: 12,
  },
  alternativeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confidenceChip: {
    height: 24,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  actionButtonStyle: {
    flex: 1,
  },
  modalContainer: {
    margin: 20,
    borderRadius: 12,
    padding: 20,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  notesInput: {
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
});
