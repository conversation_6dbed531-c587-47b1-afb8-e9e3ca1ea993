import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import messaging from '@react-native-firebase/messaging';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { appleAuth } from '@invertase/react-native-apple-authentication';
import { Platform } from 'react-native';

export interface FirebaseUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  providerId: string;
}

export interface AuthResult {
  success: boolean;
  user?: FirebaseUser;
  error?: string;
}

class FirebaseService {
  private initialized = false;

  async initialize() {
    if (this.initialized) return;

    try {
      // Configure Google Sign-In
      GoogleSignin.configure({
        webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
        offlineAccess: true,
      });

      // Request notification permissions
      await this.requestNotificationPermission();

      this.initialized = true;
      console.log('Firebase service initialized');
    } catch (error) {
      console.error('Firebase initialization error:', error);
    }
  }

  // Authentication Methods
  async signInWithEmail(email: string, password: string): Promise<AuthResult> {
    try {
      const userCredential = await auth().signInWithEmailAndPassword(email, password);
      return {
        success: true,
        user: this.formatUser(userCredential.user),
      };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code),
      };
    }
  }

  async signUpWithEmail(email: string, password: string): Promise<AuthResult> {
    try {
      const userCredential = await auth().createUserWithEmailAndPassword(email, password);
      
      // Send email verification
      await userCredential.user.sendEmailVerification();
      
      return {
        success: true,
        user: this.formatUser(userCredential.user),
      };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code),
      };
    }
  }

  async signInWithGoogle(): Promise<AuthResult> {
    try {
      // Check if device supports Google Play
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      
      // Get the user's ID token
      const { idToken } = await GoogleSignin.signIn();
      
      // Create a Google credential with the token
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      
      // Sign-in the user with the credential
      const userCredential = await auth().signInWithCredential(googleCredential);
      
      return {
        success: true,
        user: this.formatUser(userCredential.user),
      };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code) || 'Google sign-in failed',
      };
    }
  }

  async signInWithApple(): Promise<AuthResult> {
    if (Platform.OS !== 'ios') {
      return {
        success: false,
        error: 'Apple Sign-In is only available on iOS',
      };
    }

    try {
      // Start the sign-in request
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });

      // Ensure Apple returned a user identityToken
      if (!appleAuthRequestResponse.identityToken) {
        throw new Error('Apple Sign-In failed - no identify token returned');
      }

      // Create a Firebase credential from the response
      const { identityToken, nonce } = appleAuthRequestResponse;
      const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

      // Sign the user in with the credential
      const userCredential = await auth().signInWithCredential(appleCredential);

      return {
        success: true,
        user: this.formatUser(userCredential.user),
      };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code) || 'Apple sign-in failed',
      };
    }
  }

  async signOut(): Promise<void> {
    try {
      await auth().signOut();
      
      // Sign out from Google if signed in
      if (await GoogleSignin.isSignedIn()) {
        await GoogleSignin.signOut();
      }
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }

  async sendPasswordResetEmail(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      await auth().sendPasswordResetEmail(email);
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code),
      };
    }
  }

  async updatePassword(newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      const user = auth().currentUser;
      if (!user) {
        return { success: false, error: 'No user signed in' };
      }

      await user.updatePassword(newPassword);
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code),
      };
    }
  }

  async updateProfile(displayName?: string, photoURL?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const user = auth().currentUser;
      if (!user) {
        return { success: false, error: 'No user signed in' };
      }

      await user.updateProfile({
        displayName: displayName || user.displayName,
        photoURL: photoURL || user.photoURL,
      });

      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code),
      };
    }
  }

  // Get current user
  getCurrentUser(): FirebaseUser | null {
    const user = auth().currentUser;
    return user ? this.formatUser(user) : null;
  }

  // Get Firebase Auth token for Supabase RLS
  async getAuthToken(): Promise<string | null> {
    try {
      const user = auth().currentUser;
      if (!user) return null;
      
      return await user.getIdToken();
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Auth state listener
  onAuthStateChanged(callback: (user: FirebaseUser | null) => void): () => void {
    return auth().onAuthStateChanged((user) => {
      callback(user ? this.formatUser(user) : null);
    });
  }

  // Push Notifications
  async requestNotificationPermission(): Promise<boolean> {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Notification permission granted');
        await this.getFCMToken();
      }

      return enabled;
    } catch (error) {
      console.error('Notification permission error:', error);
      return false;
    }
  }

  async getFCMToken(): Promise<string | null> {
    try {
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      return token;
    } catch (error) {
      console.error('FCM token error:', error);
      return null;
    }
  }

  onNotificationReceived(callback: (message: any) => void): () => void {
    return messaging().onMessage(callback);
  }

  onBackgroundNotification(callback: (message: any) => void): void {
    messaging().setBackgroundMessageHandler(callback);
  }

  // Helper methods
  private formatUser(user: FirebaseAuthTypes.User): FirebaseUser {
    return {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      providerId: user.providerData[0]?.providerId || 'firebase',
    };
  }

  private getAuthErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'No account found with this email address';
      case 'auth/wrong-password':
        return 'Incorrect password';
      case 'auth/email-already-in-use':
        return 'An account already exists with this email address';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters';
      case 'auth/invalid-email':
        return 'Invalid email address';
      case 'auth/user-disabled':
        return 'This account has been disabled';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later';
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection';
      default:
        return 'Authentication failed. Please try again';
    }
  }
}

export const firebaseService = new FirebaseService();
