import * as FileSystem from 'expo-file-system';

const GEMINI_API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
const GEMINI_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

export interface GeminiIdentificationResult {
  name: string;
  scientific_name?: string;
  confidence: number;
  category: 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect';
  alternatives?: Array<{
    name: string;
    scientific_name?: string;
    confidence: number;
  }>;
  fact_sheet: {
    description: string;
    key_features: string[];
    interesting_facts: string[];
    category_specific: any;
  };
}

export interface ScanOptions {
  method: 'camera' | 'gallery' | 'barcode' | 'voice';
  location?: {
    latitude: number;
    longitude: number;
  };
  region?: string;
  language?: string;
}

class GeminiService {
  private async encodeImageToBase64(imageUri: string): Promise<string> {
    try {
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      return base64;
    } catch (error) {
      console.error('Error encoding image to base64:', error);
      throw new Error('Failed to process image');
    }
  }

  private createIdentificationPrompt(options: ScanOptions): string {
    const basePrompt = `
You are an expert AI identification system. Analyze the provided image and identify the object with high accuracy.

IMPORTANT: Respond ONLY with valid JSON in this exact format:
{
  "name": "Common name of the identified object",
  "scientific_name": "Scientific name if applicable",
  "confidence": 0.95,
  "category": "food|plant|animal|rock|coin|insect",
  "alternatives": [
    {
      "name": "Alternative identification 1",
      "scientific_name": "Scientific name if applicable",
      "confidence": 0.85
    }
  ],
  "fact_sheet": {
    "description": "Detailed description of the object",
    "key_features": ["Feature 1", "Feature 2", "Feature 3"],
    "interesting_facts": ["Fact 1", "Fact 2", "Fact 3"],
    "category_specific": {
      // Category-specific information based on the identified category
    }
  }
}

Category-specific information requirements:
- FOOD: Include nutritional info, recipes, storage tips, origin
- PLANT: Include care instructions, growth info, toxicity, habitat
- ANIMAL: Include habitat, diet, behavior, conservation status
- ROCK/MINERAL: Include geological properties, formation, uses, value
- COIN: Include denomination, year, mint, value, historical context
- INSECT: Include lifecycle, habitat, ecological role, identification features

${options.region ? `Focus on species/items commonly found in: ${options.region}` : ''}
${options.location ? `Consider geographical context: lat ${options.location.latitude}, lng ${options.location.longitude}` : ''}

Provide confidence scores between 0.1 and 1.0. If confidence is below 0.7, include 2-3 alternatives.
`;

    return basePrompt;
  }

  async identifyImage(imageUri: string, options: ScanOptions): Promise<GeminiIdentificationResult> {
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured');
    }

    try {
      const base64Image = await this.encodeImageToBase64(imageUri);
      const prompt = this.createIdentificationPrompt(options);

      const response = await fetch(`${GEMINI_ENDPOINT}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: 'image/jpeg',
                  data: base64Image
                }
              }
            ]
          }],
          generationConfig: {
            temperature: 0.1,
            topK: 1,
            topP: 1,
            maxOutputTokens: 2048,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Gemini API error:', errorData);
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response from Gemini API');
      }

      const textResponse = data.candidates[0].content.parts[0].text;
      
      // Clean up the response to extract JSON
      const jsonMatch = textResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const result = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (!result.name || !result.category || typeof result.confidence !== 'number') {
        throw new Error('Invalid response structure from Gemini API');
      }

      // Ensure confidence is within valid range
      result.confidence = Math.max(0.1, Math.min(1.0, result.confidence));

      return result as GeminiIdentificationResult;

    } catch (error) {
      console.error('Gemini identification error:', error);
      
      if (error instanceof SyntaxError) {
        throw new Error('Failed to parse AI response');
      }
      
      if (error.message.includes('API request failed')) {
        throw error;
      }
      
      throw new Error('Failed to identify object. Please try again.');
    }
  }

  async identifyByVoice(audioDescription: string, options: ScanOptions): Promise<GeminiIdentificationResult> {
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured');
    }

    const prompt = `
Based on this voice description: "${audioDescription}"

${this.createIdentificationPrompt(options)}

Note: This identification is based on a voice description, so confidence may be lower than image-based identification.
`;

    try {
      const response = await fetch(`${GEMINI_ENDPOINT}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.2,
            topK: 1,
            topP: 1,
            maxOutputTokens: 2048,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const textResponse = data.candidates[0].content.parts[0].text;
      
      const jsonMatch = textResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const result = JSON.parse(jsonMatch[0]);
      
      // Lower confidence for voice-based identification
      result.confidence = Math.max(0.1, Math.min(0.8, result.confidence * 0.8));

      return result as GeminiIdentificationResult;

    } catch (error) {
      console.error('Voice identification error:', error);
      throw new Error('Failed to identify object from voice description');
    }
  }

  async enhanceFactSheet(category: string, name: string, scientificName?: string): Promise<any> {
    // This method can be used to get more detailed information about an identified object
    const prompt = `
Provide comprehensive, detailed information about: ${name} ${scientificName ? `(${scientificName})` : ''}
Category: ${category}

Return detailed JSON with category-specific information suitable for an educational fact sheet.
`;

    try {
      const response = await fetch(`${GEMINI_ENDPOINT}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 1024,
          }
        }),
      });

      const data = await response.json();
      const textResponse = data.candidates[0].content.parts[0].text;
      
      const jsonMatch = textResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      return { enhanced_info: textResponse };

    } catch (error) {
      console.error('Fact sheet enhancement error:', error);
      return null;
    }
  }
}

export const geminiService = new GeminiService();
