import { firebaseService } from './firebaseService';
import { tursoService } from './tursoService';
import { syncService } from './syncService';
import { useAuthStore } from '../stores/authStoreFirebase';
import { useScanStore } from '../stores/scanStoreTurso';

export interface InitializationStatus {
  firebase: boolean;
  turso: boolean;
  sync: boolean;
  auth: boolean;
  scan: boolean;
  overall: boolean;
  error?: string;
}

class InitializationService {
  private initialized = false;
  private status: InitializationStatus = {
    firebase: false,
    turso: false,
    sync: false,
    auth: false,
    scan: false,
    overall: false,
  };

  async initialize(): Promise<InitializationStatus> {
    if (this.initialized) {
      return this.status;
    }

    console.log('🚀 Initializing Apex services...');

    try {
      // 1. Initialize Firebase (Authentication & FCM)
      console.log('📱 Initializing Firebase...');
      await firebaseService.initialize();
      this.status.firebase = true;
      console.log('✅ Firebase initialized');

      // 2. Initialize Turso (Local Database)
      console.log('💾 Initializing Turso...');
      await tursoService.initialize();
      this.status.turso = true;
      console.log('✅ Turso initialized');

      // 3. Initialize Sync Service
      console.log('🔄 Initializing Sync Service...');
      await syncService.initialize();
      this.status.sync = true;
      console.log('✅ Sync Service initialized');

      // 4. Initialize Auth Store
      console.log('🔐 Initializing Auth Store...');
      await useAuthStore.getState().initialize();
      this.status.auth = true;
      console.log('✅ Auth Store initialized');

      // 5. Initialize Scan Store
      console.log('📸 Initializing Scan Store...');
      await useScanStore.getState().initialize();
      this.status.scan = true;
      console.log('✅ Scan Store initialized');

      this.status.overall = true;
      this.initialized = true;

      console.log('🎉 All Apex services initialized successfully!');
      return this.status;

    } catch (error) {
      console.error('❌ Initialization failed:', error);
      this.status.error = error instanceof Error ? error.message : 'Unknown error';
      this.status.overall = false;
      return this.status;
    }
  }

  getStatus(): InitializationStatus {
    return { ...this.status };
  }

  isInitialized(): boolean {
    return this.initialized && this.status.overall;
  }

  async reinitialize(): Promise<InitializationStatus> {
    this.initialized = false;
    this.status = {
      firebase: false,
      turso: false,
      sync: false,
      auth: false,
      scan: false,
      overall: false,
    };
    
    return await this.initialize();
  }

  // Health check for all services
  async healthCheck(): Promise<{
    firebase: boolean;
    turso: boolean;
    sync: boolean;
    network: boolean;
    overall: boolean;
  }> {
    const health = {
      firebase: false,
      turso: false,
      sync: false,
      network: false,
      overall: false,
    };

    try {
      // Check Firebase
      const firebaseUser = firebaseService.getCurrentUser();
      health.firebase = firebaseUser !== null;

      // Check Turso (try a simple query)
      try {
        await tursoService.getCache('health_check');
        health.turso = true;
      } catch {
        health.turso = false;
      }

      // Check Sync Service
      const syncStatus = syncService.getStatus();
      health.sync = !syncStatus.isSyncing; // Not stuck in sync

      // Check Network
      health.network = syncStatus.isOnline;

      health.overall = health.firebase && health.turso && health.sync;

    } catch (error) {
      console.error('Health check failed:', error);
    }

    return health;
  }

  // Cleanup method for app shutdown
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up Apex services...');
      
      // Cleanup sync service
      syncService.destroy();
      
      // Close Turso connection
      await tursoService.close();
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }
  }
}

export const initializationService = new InitializationService();
