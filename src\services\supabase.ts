import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';

// TODO: Replace with your actual Supabase URL and anon key
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: false, // Firebase handles auth
    persistSession: false,   // Firebase handles sessions
    detectSessionInUrl: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'apex-bioscan-app',
    },
  },
});

// Helper function to set Firebase auth token for Supabase RLS
export const setSupabaseAuthToken = async (firebaseToken: string) => {
  try {
    // Set the auth header for subsequent requests
    supabase.rest.headers['Authorization'] = `Bearer ${firebaseToken}`;

    // You can also set a custom session if needed for RLS
    // This depends on your Supabase RLS policies configuration
    await supabase.auth.setSession({
      access_token: firebaseToken,
      refresh_token: '', // Not used with Firebase
    });
  } catch (error) {
    console.error('Error setting Supabase auth token:', error);
  }
};

// Database schema types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          username: string | null;
          avatar_url: string | null;
          subscription_tier: 'free' | 'premium';
          preferences: {
            theme: 'light' | 'dark' | 'auto';
            language: string;
            notifications: boolean;
            location_sharing: boolean;
          };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          username?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'premium';
          preferences?: {
            theme: 'light' | 'dark' | 'auto';
            language: string;
            notifications: boolean;
            location_sharing: boolean;
          };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          username?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'premium';
          preferences?: {
            theme: 'light' | 'dark' | 'auto';
            language: string;
            notifications: boolean;
            location_sharing: boolean;
          };
          updated_at?: string;
        };
      };
      scans: {
        Row: {
          id: string;
          user_id: string;
          image_url: string;
          category: 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect';
          identification: {
            name: string;
            scientific_name?: string;
            confidence: number;
            alternatives?: Array<{
              name: string;
              confidence: number;
            }>;
          };
          location: {
            latitude: number;
            longitude: number;
            address?: string;
          } | null;
          metadata: {
            scan_method: 'camera' | 'gallery' | 'barcode' | 'voice';
            timestamp: string;
            device_info?: any;
          };
          fact_sheet: any | null;
          user_notes: string | null;
          tags: string[] | null;
          is_favorite: boolean;
          is_verified: boolean;
          verification_source: 'community' | 'expert' | 'ai' | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          image_url: string;
          category: 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect';
          identification: {
            name: string;
            scientific_name?: string;
            confidence: number;
            alternatives?: Array<{
              name: string;
              confidence: number;
            }>;
          };
          location?: {
            latitude: number;
            longitude: number;
            address?: string;
          } | null;
          metadata: {
            scan_method: 'camera' | 'gallery' | 'barcode' | 'voice';
            timestamp: string;
            device_info?: any;
          };
          fact_sheet?: any | null;
          user_notes?: string | null;
          tags?: string[] | null;
          is_favorite?: boolean;
          verification_source?: 'community' | 'expert' | 'ai' | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          image_url?: string;
          category?: 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect';
          identification?: {
            name: string;
            scientific_name?: string;
            confidence: number;
            alternatives?: Array<{
              name: string;
              confidence: number;
            }>;
          };
          location?: {
            latitude: number;
            longitude: number;
            address?: string;
          } | null;
          fact_sheet?: any | null;
          user_notes?: string | null;
          tags?: string[] | null;
          is_favorite?: boolean;
          is_verified?: boolean;
          verification_source?: 'community' | 'expert' | 'ai' | null;
          updated_at?: string;
        };
      };
      achievements: {
        Row: {
          id: string;
          user_id: string;
          achievement_type: string;
          achievement_data: any;
          earned_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          achievement_type: string;
          achievement_data: any;
          earned_at?: string;
        };
        Update: {
          achievement_data?: any;
        };
      };
      todos: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          description: string | null;
          due_date: string | null;
          priority: 'low' | 'medium' | 'high';
          category: string | null;
          is_completed: boolean;
          scan_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          description?: string | null;
          due_date?: string | null;
          priority?: 'low' | 'medium' | 'high';
          category?: string | null;
          is_completed?: boolean;
          scan_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          title?: string;
          description?: string | null;
          due_date?: string | null;
          priority?: 'low' | 'medium' | 'high';
          category?: string | null;
          is_completed?: boolean;
          updated_at?: string;
        };
      };
    };
  };
}
