import { supabase } from './supabase';
import { tursoService, SyncQueueItem } from './tursoService';
import { firebaseService } from './firebaseService';
import NetInfo from '@react-native-community/netinfo';

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  pendingItems: number;
  failedItems: number;
}

export interface ConflictResolution {
  strategy: 'local_wins' | 'remote_wins' | 'merge' | 'manual';
  resolvedData?: any;
}

class SyncService {
  private isOnline = false;
  private isSyncing = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private lastSyncTime: Date | null = null;
  private listeners: Array<(status: SyncStatus) => void> = [];

  async initialize() {
    // Monitor network connectivity
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      // If we just came online, trigger sync
      if (!wasOnline && this.isOnline) {
        this.triggerSync();
      }
      
      this.notifyListeners();
    });

    // Start periodic sync when online
    this.startPeriodicSync();
    
    console.log('Sync service initialized');
  }

  private startPeriodicSync() {
    // Sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.isSyncing) {
        this.triggerSync();
      }
    }, 30000);
  }

  async triggerSync(): Promise<void> {
    if (!this.isOnline || this.isSyncing) return;

    this.isSyncing = true;
    this.notifyListeners();

    try {
      // Get Firebase auth token for Supabase RLS
      const authToken = await firebaseService.getAuthToken();
      if (!authToken) {
        console.log('No auth token available, skipping sync');
        return;
      }

      // Set auth token for Supabase
      await supabase.auth.setSession({
        access_token: authToken,
        refresh_token: '', // Firebase handles refresh
      });

      // Sync pending local changes to cloud
      await this.syncLocalToCloud();
      
      // Sync cloud changes to local
      await this.syncCloudToLocal();
      
      this.lastSyncTime = new Date();
      console.log('Sync completed successfully');
      
    } catch (error) {
      console.error('Sync error:', error);
    } finally {
      this.isSyncing = false;
      this.notifyListeners();
    }
  }

  private async syncLocalToCloud(): Promise<void> {
    const pendingItems = await tursoService.getPendingSyncItems();
    
    for (const item of pendingItems) {
      try {
        await tursoService.updateSyncItemStatus(item.id, 'syncing');
        
        switch (item.operation) {
          case 'INSERT':
            await this.handleInsert(item);
            break;
          case 'UPDATE':
            await this.handleUpdate(item);
            break;
          case 'DELETE':
            await this.handleDelete(item);
            break;
        }
        
        await tursoService.updateSyncItemStatus(item.id, 'completed');
        
      } catch (error) {
        console.error(`Sync error for item ${item.id}:`, error);
        await tursoService.updateSyncItemStatus(
          item.id, 
          'failed', 
          error.message
        );
      }
    }
  }

  private async handleInsert(item: SyncQueueItem): Promise<void> {
    const { table, data } = item;
    
    switch (table) {
      case 'scans':
        await this.insertScan(data);
        break;
      case 'profiles':
        await this.insertProfile(data);
        break;
      case 'todos':
        await this.insertTodo(data);
        break;
      case 'achievements':
        await this.insertAchievement(data);
        break;
    }
  }

  private async handleUpdate(item: SyncQueueItem): Promise<void> {
    const { table, data } = item;
    
    switch (table) {
      case 'scans':
        await this.updateScan(data);
        break;
      case 'profiles':
        await this.updateProfile(data);
        break;
      case 'todos':
        await this.updateTodo(data);
        break;
    }
  }

  private async handleDelete(item: SyncQueueItem): Promise<void> {
    const { table, data } = item;
    
    switch (table) {
      case 'scans':
        await supabase.from('scans').delete().eq('id', data.id);
        break;
      case 'todos':
        await supabase.from('todos').delete().eq('id', data.id);
        break;
    }
  }

  // Specific sync operations
  private async insertScan(scanData: any): Promise<void> {
    // Upload image to Supabase Storage first
    let imageUrl = scanData.image_uri;
    
    if (scanData.image_uri && scanData.image_uri.startsWith('file://')) {
      imageUrl = await this.uploadImage(scanData.image_uri, `scans/${scanData.id}`);
    }

    const { error } = await supabase.from('scans').insert({
      id: scanData.id,
      user_id: scanData.user_id,
      image_url: imageUrl,
      category: scanData.category,
      identification: scanData.identification,
      location: scanData.location,
      metadata: scanData.metadata,
      fact_sheet: scanData.fact_sheet,
      user_notes: scanData.user_notes,
      tags: scanData.tags,
      is_favorite: scanData.is_favorite,
      is_verified: scanData.is_verified,
      verification_source: scanData.verification_source,
    });

    if (error) throw error;
  }

  private async updateScan(scanData: any): Promise<void> {
    const { error } = await supabase
      .from('scans')
      .update(scanData)
      .eq('id', scanData.id);

    if (error) throw error;
  }

  private async insertProfile(profileData: any): Promise<void> {
    const { error } = await supabase.from('profiles').insert(profileData);
    if (error) throw error;
  }

  private async updateProfile(profileData: any): Promise<void> {
    const { error } = await supabase
      .from('profiles')
      .update(profileData)
      .eq('id', profileData.id);

    if (error) throw error;
  }

  private async insertTodo(todoData: any): Promise<void> {
    const { error } = await supabase.from('todos').insert(todoData);
    if (error) throw error;
  }

  private async updateTodo(todoData: any): Promise<void> {
    const { error } = await supabase
      .from('todos')
      .update(todoData)
      .eq('id', todoData.id);

    if (error) throw error;
  }

  private async insertAchievement(achievementData: any): Promise<void> {
    const { error } = await supabase.from('achievements').insert(achievementData);
    if (error) throw error;
  }

  private async uploadImage(localUri: string, path: string): Promise<string> {
    try {
      // Read file as blob (implementation depends on platform)
      const response = await fetch(localUri);
      const blob = await response.blob();
      
      const { data, error } = await supabase.storage
        .from('images')
        .upload(path, blob, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('images')
        .getPublicUrl(data.path);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Image upload error:', error);
      return localUri; // Fallback to local URI
    }
  }

  private async syncCloudToLocal(): Promise<void> {
    const user = firebaseService.getCurrentUser();
    if (!user) return;

    try {
      // Get last sync timestamp from cache
      const lastSync = await tursoService.getCache('last_cloud_sync');
      const since = lastSync ? new Date(lastSync) : new Date(0);

      // Fetch updated records from cloud
      const { data: scans } = await supabase
        .from('scans')
        .select('*')
        .eq('user_id', user.uid)
        .gte('updated_at', since.toISOString());

      const { data: todos } = await supabase
        .from('todos')
        .select('*')
        .eq('user_id', user.uid)
        .gte('updated_at', since.toISOString());

      // Update local database with cloud changes
      if (scans) {
        for (const scan of scans) {
          await this.mergeCloudScan(scan);
        }
      }

      if (todos) {
        for (const todo of todos) {
          await this.mergeCloudTodo(todo);
        }
      }

      // Update last sync timestamp
      await tursoService.setCache('last_cloud_sync', new Date().toISOString());

    } catch (error) {
      console.error('Cloud to local sync error:', error);
    }
  }

  private async mergeCloudScan(cloudScan: any): Promise<void> {
    // Check if local version exists and handle conflicts
    // For now, implement simple "cloud wins" strategy
    // TODO: Implement more sophisticated conflict resolution
    
    try {
      await tursoService.insertScan({
        ...cloudScan,
        image_uri: cloudScan.image_url, // Map cloud URL to local URI field
      });
    } catch (error) {
      // If insert fails (record exists), try update
      await tursoService.updateScan(cloudScan.id, cloudScan);
    }
  }

  private async mergeCloudTodo(cloudTodo: any): Promise<void> {
    // Similar conflict resolution for todos
    // TODO: Implement proper merge logic
    console.log('Merging cloud todo:', cloudTodo.id);
  }

  // Public API
  async forcSync(): Promise<void> {
    if (!this.isOnline) {
      throw new Error('Cannot sync while offline');
    }
    
    await this.triggerSync();
  }

  getStatus(): SyncStatus {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      lastSyncTime: this.lastSyncTime,
      pendingItems: 0, // TODO: Get actual count
      failedItems: 0,  // TODO: Get actual count
    };
  }

  onStatusChange(callback: (status: SyncStatus) => void): () => void {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    const status = this.getStatus();
    this.listeners.forEach(callback => callback(status));
  }

  destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.listeners = [];
  }
}

export const syncService = new SyncService();
