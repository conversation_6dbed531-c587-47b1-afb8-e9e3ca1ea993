import { createClient, Client } from '@libsql/client';
import { ScanResult } from '../stores/scanStore';

export interface TursoConfig {
  url: string;
  authToken?: string;
}

export interface SyncQueueItem {
  id: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  data: any;
  timestamp: string;
  retries: number;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
}

class TursoService {
  private client: Client | null = null;
  private initialized = false;

  async initialize(config?: TursoConfig) {
    if (this.initialized) return;

    try {
      // For local development, use file-based SQLite
      // For production, use Turso cloud URL
      const dbConfig = config || {
        url: 'file:local.db', // Local SQLite file
      };

      this.client = createClient(dbConfig);
      
      await this.createTables();
      this.initialized = true;
      console.log('Turso database initialized');
    } catch (error) {
      console.error('Turso initialization error:', error);
      throw error;
    }
  }

  private async createTables() {
    if (!this.client) throw new Error('Database not initialized');

    const tables = [
      // User profiles table
      `CREATE TABLE IF NOT EXISTS profiles (
        id TEXT PRIMARY KEY,
        username TEXT,
        avatar_url TEXT,
        subscription_tier TEXT DEFAULT 'free',
        preferences TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'pending'
      )`,

      // Scans table
      `CREATE TABLE IF NOT EXISTS scans (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        image_uri TEXT NOT NULL,
        image_url TEXT,
        category TEXT NOT NULL,
        identification TEXT NOT NULL,
        location TEXT,
        metadata TEXT NOT NULL,
        fact_sheet TEXT,
        user_notes TEXT,
        tags TEXT,
        is_favorite INTEGER DEFAULT 0,
        is_verified INTEGER DEFAULT 0,
        verification_source TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'pending'
      )`,

      // Todos table
      `CREATE TABLE IF NOT EXISTS todos (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        due_date TEXT,
        priority TEXT DEFAULT 'medium',
        category TEXT,
        is_completed INTEGER DEFAULT 0,
        scan_id TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'pending'
      )`,

      // Achievements table
      `CREATE TABLE IF NOT EXISTS achievements (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        achievement_type TEXT NOT NULL,
        achievement_data TEXT NOT NULL,
        earned_at TEXT DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'pending'
      )`,

      // Sync queue table
      `CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        operation TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
        retries INTEGER DEFAULT 0,
        status TEXT DEFAULT 'pending',
        error_message TEXT
      )`,

      // Cache table for offline content
      `CREATE TABLE IF NOT EXISTS cache (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        expires_at TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const table of tables) {
      await this.client.execute(table);
    }

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_scans_user_id ON scans(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_scans_category ON scans(category)',
      'CREATE INDEX IF NOT EXISTS idx_scans_created_at ON scans(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_todos_user_id ON todos(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status)',
      'CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON cache(expires_at)',
    ];

    for (const index of indexes) {
      await this.client.execute(index);
    }
  }

  // Scan operations
  async insertScan(scan: ScanResult): Promise<void> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      await this.client.execute({
        sql: `INSERT INTO scans (
          id, user_id, image_uri, category, identification, location, 
          metadata, fact_sheet, user_notes, tags, is_favorite, 
          is_verified, verification_source, sync_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        args: [
          scan.id,
          scan.user_id,
          scan.image_uri,
          scan.category,
          JSON.stringify(scan.identification),
          scan.location ? JSON.stringify(scan.location) : null,
          JSON.stringify(scan.metadata),
          scan.fact_sheet ? JSON.stringify(scan.fact_sheet) : null,
          scan.user_notes || null,
          scan.tags ? JSON.stringify(scan.tags) : null,
          scan.is_favorite ? 1 : 0,
          scan.is_verified ? 1 : 0,
          scan.verification_source || null,
          'pending'
        ]
      });

      // Add to sync queue
      await this.addToSyncQueue('INSERT', 'scans', scan.id, scan);
    } catch (error) {
      console.error('Error inserting scan:', error);
      throw error;
    }
  }

  async updateScan(id: string, updates: Partial<ScanResult>): Promise<void> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      const setClause = [];
      const args = [];

      if (updates.user_notes !== undefined) {
        setClause.push('user_notes = ?');
        args.push(updates.user_notes);
      }
      if (updates.tags !== undefined) {
        setClause.push('tags = ?');
        args.push(JSON.stringify(updates.tags));
      }
      if (updates.is_favorite !== undefined) {
        setClause.push('is_favorite = ?');
        args.push(updates.is_favorite ? 1 : 0);
      }
      if (updates.is_verified !== undefined) {
        setClause.push('is_verified = ?');
        args.push(updates.is_verified ? 1 : 0);
      }

      if (setClause.length === 0) return;

      setClause.push('updated_at = CURRENT_TIMESTAMP');
      setClause.push('sync_status = ?');
      args.push('pending');
      args.push(id);

      await this.client.execute({
        sql: `UPDATE scans SET ${setClause.join(', ')} WHERE id = ?`,
        args
      });

      // Add to sync queue
      await this.addToSyncQueue('UPDATE', 'scans', id, updates);
    } catch (error) {
      console.error('Error updating scan:', error);
      throw error;
    }
  }

  async deleteScan(id: string): Promise<void> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      await this.client.execute({
        sql: 'DELETE FROM scans WHERE id = ?',
        args: [id]
      });

      // Add to sync queue
      await this.addToSyncQueue('DELETE', 'scans', id, { id });
    } catch (error) {
      console.error('Error deleting scan:', error);
      throw error;
    }
  }

  async getScans(userId: string, limit = 50, offset = 0): Promise<ScanResult[]> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      const result = await this.client.execute({
        sql: `SELECT * FROM scans WHERE user_id = ? 
              ORDER BY created_at DESC LIMIT ? OFFSET ?`,
        args: [userId, limit, offset]
      });

      return result.rows.map(row => this.formatScanFromRow(row));
    } catch (error) {
      console.error('Error getting scans:', error);
      throw error;
    }
  }

  async searchScans(userId: string, query: string, category?: string): Promise<ScanResult[]> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      let sql = `SELECT * FROM scans WHERE user_id = ? AND (
        identification LIKE ? OR user_notes LIKE ? OR tags LIKE ?
      )`;
      let args = [userId, `%${query}%`, `%${query}%`, `%${query}%`];

      if (category) {
        sql += ' AND category = ?';
        args.push(category);
      }

      sql += ' ORDER BY created_at DESC';

      const result = await this.client.execute({ sql, args });
      return result.rows.map(row => this.formatScanFromRow(row));
    } catch (error) {
      console.error('Error searching scans:', error);
      throw error;
    }
  }

  // Sync queue operations
  async addToSyncQueue(operation: string, table: string, recordId: string, data: any): Promise<void> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      const id = `${operation}_${table}_${recordId}_${Date.now()}`;
      await this.client.execute({
        sql: `INSERT INTO sync_queue (id, operation, table_name, record_id, data) 
              VALUES (?, ?, ?, ?, ?)`,
        args: [id, operation, table, recordId, JSON.stringify(data)]
      });
    } catch (error) {
      console.error('Error adding to sync queue:', error);
    }
  }

  async getPendingSyncItems(): Promise<SyncQueueItem[]> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      const result = await this.client.execute({
        sql: `SELECT * FROM sync_queue WHERE status = 'pending' 
              ORDER BY timestamp ASC LIMIT 50`
      });

      return result.rows.map(row => ({
        id: row.id as string,
        operation: row.operation as 'INSERT' | 'UPDATE' | 'DELETE',
        table: row.table_name as string,
        data: JSON.parse(row.data as string),
        timestamp: row.timestamp as string,
        retries: row.retries as number,
        status: row.status as 'pending' | 'syncing' | 'completed' | 'failed'
      }));
    } catch (error) {
      console.error('Error getting pending sync items:', error);
      return [];
    }
  }

  async updateSyncItemStatus(id: string, status: string, errorMessage?: string): Promise<void> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      await this.client.execute({
        sql: `UPDATE sync_queue SET status = ?, error_message = ?, 
              retries = retries + 1 WHERE id = ?`,
        args: [status, errorMessage || null, id]
      });
    } catch (error) {
      console.error('Error updating sync item status:', error);
    }
  }

  // Cache operations
  async setCache(key: string, value: any, expiresIn?: number): Promise<void> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      const expiresAt = expiresIn ? 
        new Date(Date.now() + expiresIn * 1000).toISOString() : null;

      await this.client.execute({
        sql: `INSERT OR REPLACE INTO cache (key, value, expires_at) VALUES (?, ?, ?)`,
        args: [key, JSON.stringify(value), expiresAt]
      });
    } catch (error) {
      console.error('Error setting cache:', error);
    }
  }

  async getCache(key: string): Promise<any | null> {
    if (!this.client) throw new Error('Database not initialized');

    try {
      const result = await this.client.execute({
        sql: `SELECT value, expires_at FROM cache WHERE key = ?`,
        args: [key]
      });

      if (result.rows.length === 0) return null;

      const row = result.rows[0];
      const expiresAt = row.expires_at as string | null;

      // Check if expired
      if (expiresAt && new Date(expiresAt) < new Date()) {
        await this.client.execute({
          sql: 'DELETE FROM cache WHERE key = ?',
          args: [key]
        });
        return null;
      }

      return JSON.parse(row.value as string);
    } catch (error) {
      console.error('Error getting cache:', error);
      return null;
    }
  }

  // Helper methods
  private formatScanFromRow(row: any): ScanResult {
    return {
      id: row.id,
      user_id: row.user_id,
      image_uri: row.image_uri,
      category: row.category,
      identification: JSON.parse(row.identification),
      location: row.location ? JSON.parse(row.location) : undefined,
      metadata: JSON.parse(row.metadata),
      fact_sheet: row.fact_sheet ? JSON.parse(row.fact_sheet) : undefined,
      user_notes: row.user_notes || undefined,
      tags: row.tags ? JSON.parse(row.tags) : undefined,
      is_favorite: Boolean(row.is_favorite),
      is_verified: Boolean(row.is_verified),
      verification_source: row.verification_source || undefined,
    };
  }

  async close(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.initialized = false;
    }
  }
}

export const tursoService = new TursoService();
