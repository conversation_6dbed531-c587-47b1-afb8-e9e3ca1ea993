import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { firebaseService, FirebaseUser } from '../services/firebaseService';
import { supabase } from '../services/supabase';
import { tursoService } from '../services/tursoService';

export interface User {
  id: string;
  email: string;
  username?: string;
  displayName?: string;
  avatar_url?: string;
  created_at: string;
  subscription_tier: 'free' | 'premium';
  emailVerified: boolean;
  providerId: string;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    notifications: boolean;
    location_sharing: boolean;
  };
}

interface AuthState {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  fcmToken: string | null;
  
  // Actions
  signInWithEmail: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUpWithEmail: (email: string, password: string, username?: string) => Promise<{ success: boolean; error?: string }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signInWithApple: () => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  sendPasswordReset: (email: string) => Promise<{ success: boolean; error?: string }>;
  updateProfile: (updates: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  refreshSession: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      firebaseUser: null,
      isLoading: false,
      isAuthenticated: false,
      fcmToken: null,

      initialize: async () => {
        set({ isLoading: true });
        
        try {
          // Initialize Firebase service
          await firebaseService.initialize();
          
          // Initialize Turso database
          await tursoService.initialize();
          
          // Set up auth state listener
          firebaseService.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
              // User is signed in, fetch/create profile
              const user = await get().createOrUpdateUserProfile(firebaseUser);
              set({
                user,
                firebaseUser,
                isAuthenticated: true,
                isLoading: false,
              });
            } else {
              // User is signed out
              set({
                user: null,
                firebaseUser: null,
                isAuthenticated: false,
                isLoading: false,
              });
            }
          });

          // Get FCM token
          const fcmToken = await firebaseService.getFCMToken();
          set({ fcmToken });

        } catch (error) {
          console.error('Auth initialization error:', error);
          set({ isLoading: false });
        }
      },

      signInWithEmail: async (email: string, password: string) => {
        set({ isLoading: true });
        
        const result = await firebaseService.signInWithEmail(email, password);
        
        if (!result.success) {
          set({ isLoading: false });
        }
        
        return result;
      },

      signUpWithEmail: async (email: string, password: string, username?: string) => {
        set({ isLoading: true });
        
        const result = await firebaseService.signUpWithEmail(email, password);
        
        if (result.success && result.user) {
          // Create user profile with username
          await get().createOrUpdateUserProfile(result.user, username);
        }
        
        if (!result.success) {
          set({ isLoading: false });
        }
        
        return result;
      },

      signInWithGoogle: async () => {
        set({ isLoading: true });
        
        const result = await firebaseService.signInWithGoogle();
        
        if (!result.success) {
          set({ isLoading: false });
        }
        
        return result;
      },

      signInWithApple: async () => {
        set({ isLoading: true });
        
        const result = await firebaseService.signInWithApple();
        
        if (!result.success) {
          set({ isLoading: false });
        }
        
        return result;
      },

      signOut: async () => {
        set({ isLoading: true });
        await firebaseService.signOut();
        // Auth state listener will handle clearing the state
      },

      sendPasswordReset: async (email: string) => {
        return await firebaseService.sendPasswordResetEmail(email);
      },

      updateProfile: async (updates: Partial<User>) => {
        const { user, firebaseUser } = get();
        if (!user || !firebaseUser) {
          return { success: false, error: 'Not authenticated' };
        }

        set({ isLoading: true });
        
        try {
          // Update Firebase profile if display name or photo changed
          if (updates.displayName || updates.avatar_url) {
            await firebaseService.updateProfile(updates.displayName, updates.avatar_url);
          }

          // Update local profile
          const updatedUser = { ...user, ...updates };
          
          // Update in Turso (will sync to Supabase)
          // TODO: Implement profile update in Turso
          
          set({
            user: updatedUser,
            isLoading: false,
          });

          return { success: true };
        } catch (error) {
          set({ isLoading: false });
          return { success: false, error: 'Update failed' };
        }
      },

      refreshSession: async () => {
        const firebaseUser = firebaseService.getCurrentUser();
        if (firebaseUser) {
          const user = await get().createOrUpdateUserProfile(firebaseUser);
          set({ 
            user, 
            firebaseUser, 
            isAuthenticated: true 
          });
        }
      },

      setLoading: (loading: boolean) => set({ isLoading: loading }),

      // Helper method to create or update user profile
      createOrUpdateUserProfile: async (firebaseUser: FirebaseUser, username?: string): Promise<User> => {
        const user: User = {
          id: firebaseUser.uid,
          email: firebaseUser.email || '',
          username: username || firebaseUser.displayName || undefined,
          displayName: firebaseUser.displayName || undefined,
          avatar_url: firebaseUser.photoURL || undefined,
          created_at: new Date().toISOString(),
          subscription_tier: 'free',
          emailVerified: firebaseUser.emailVerified,
          providerId: firebaseUser.providerId,
          preferences: {
            theme: 'auto',
            language: 'en',
            notifications: true,
            location_sharing: false,
          },
        };

        // TODO: Store profile in Turso (will sync to Supabase)
        // For now, just return the user object
        
        return user;
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        firebaseUser: state.firebaseUser,
        isAuthenticated: state.isAuthenticated,
        fcmToken: state.fcmToken,
      }),
    }
  )
);
