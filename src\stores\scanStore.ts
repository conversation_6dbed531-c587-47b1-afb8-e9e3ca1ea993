import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ScanResult {
  id: string;
  user_id: string;
  image_uri: string;
  category: 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect';
  identification: {
    name: string;
    scientific_name?: string;
    confidence: number;
    alternatives?: Array<{
      name: string;
      confidence: number;
    }>;
  };
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  metadata: {
    scan_method: 'camera' | 'gallery' | 'barcode' | 'voice';
    timestamp: string;
    device_info?: any;
  };
  fact_sheet?: any;
  user_notes?: string;
  tags?: string[];
  is_favorite: boolean;
  is_verified: boolean;
  verification_source?: 'community' | 'expert' | 'ai';
}

interface ScanState {
  scans: ScanResult[];
  currentScan: ScanResult | null;
  isScanning: boolean;
  isProcessing: boolean;
  scanHistory: ScanResult[];
  favorites: ScanResult[];
  
  // Filters and search
  searchQuery: string;
  categoryFilter: string | null;
  dateFilter: { start?: Date; end?: Date } | null;
  
  // Actions
  startScan: () => void;
  stopScan: () => void;
  setProcessing: (processing: boolean) => void;
  addScan: (scan: ScanResult) => void;
  updateScan: (id: string, updates: Partial<ScanResult>) => void;
  deleteScan: (id: string) => void;
  toggleFavorite: (id: string) => void;
  setCurrentScan: (scan: ScanResult | null) => void;
  
  // Search and filter
  setSearchQuery: (query: string) => void;
  setCategoryFilter: (category: string | null) => void;
  setDateFilter: (filter: { start?: Date; end?: Date } | null) => void;
  getFilteredScans: () => ScanResult[];
  
  // Sync
  syncScans: () => Promise<void>;
  loadScansFromCache: () => void;
}

export const useScanStore = create<ScanState>()(
  persist(
    (set, get) => ({
      scans: [],
      currentScan: null,
      isScanning: false,
      isProcessing: false,
      scanHistory: [],
      favorites: [],
      searchQuery: '',
      categoryFilter: null,
      dateFilter: null,

      startScan: () => set({ isScanning: true }),
      
      stopScan: () => set({ isScanning: false }),
      
      setProcessing: (processing: boolean) => set({ isProcessing: processing }),
      
      addScan: (scan: ScanResult) => {
        const { scans } = get();
        const newScans = [scan, ...scans];
        set({
          scans: newScans,
          scanHistory: newScans,
          currentScan: scan,
        });
      },
      
      updateScan: (id: string, updates: Partial<ScanResult>) => {
        const { scans } = get();
        const updatedScans = scans.map(scan =>
          scan.id === id ? { ...scan, ...updates } : scan
        );
        set({
          scans: updatedScans,
          scanHistory: updatedScans,
        });
      },
      
      deleteScan: (id: string) => {
        const { scans } = get();
        const filteredScans = scans.filter(scan => scan.id !== id);
        set({
          scans: filteredScans,
          scanHistory: filteredScans,
        });
      },
      
      toggleFavorite: (id: string) => {
        const { scans } = get();
        const updatedScans = scans.map(scan =>
          scan.id === id ? { ...scan, is_favorite: !scan.is_favorite } : scan
        );
        const favorites = updatedScans.filter(scan => scan.is_favorite);
        set({
          scans: updatedScans,
          scanHistory: updatedScans,
          favorites,
        });
      },
      
      setCurrentScan: (scan: ScanResult | null) => set({ currentScan: scan }),
      
      setSearchQuery: (query: string) => set({ searchQuery: query }),
      
      setCategoryFilter: (category: string | null) => set({ categoryFilter: category }),
      
      setDateFilter: (filter: { start?: Date; end?: Date } | null) => set({ dateFilter: filter }),
      
      getFilteredScans: () => {
        const { scans, searchQuery, categoryFilter, dateFilter } = get();
        
        return scans.filter(scan => {
          // Search query filter
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const matchesName = scan.identification.name.toLowerCase().includes(query);
            const matchesScientific = scan.identification.scientific_name?.toLowerCase().includes(query);
            const matchesNotes = scan.user_notes?.toLowerCase().includes(query);
            const matchesTags = scan.tags?.some(tag => tag.toLowerCase().includes(query));
            
            if (!matchesName && !matchesScientific && !matchesNotes && !matchesTags) {
              return false;
            }
          }
          
          // Category filter
          if (categoryFilter && scan.category !== categoryFilter) {
            return false;
          }
          
          // Date filter
          if (dateFilter) {
            const scanDate = new Date(scan.metadata.timestamp);
            if (dateFilter.start && scanDate < dateFilter.start) return false;
            if (dateFilter.end && scanDate > dateFilter.end) return false;
          }
          
          return true;
        });
      },
      
      syncScans: async () => {
        // TODO: Implement sync with Supabase
        console.log('Syncing scans...');
      },
      
      loadScansFromCache: () => {
        // This will be called on app startup to load cached scans
        const { scans } = get();
        const favorites = scans.filter(scan => scan.is_favorite);
        set({
          scanHistory: scans,
          favorites,
        });
      },
    }),
    {
      name: 'scan-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        scans: state.scans,
        favorites: state.favorites,
        scanHistory: state.scanHistory,
      }),
    }
  )
);
