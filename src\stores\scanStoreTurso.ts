import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { tursoService } from '../services/tursoService';
import { syncService } from '../services/syncService';

export interface ScanResult {
  id: string;
  user_id: string;
  image_uri: string;
  category: 'food' | 'plant' | 'animal' | 'rock' | 'coin' | 'insect';
  identification: {
    name: string;
    scientific_name?: string;
    confidence: number;
    alternatives?: Array<{
      name: string;
      confidence: number;
    }>;
  };
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  metadata: {
    scan_method: 'camera' | 'gallery' | 'barcode' | 'voice';
    timestamp: string;
    device_info?: any;
  };
  fact_sheet?: any;
  user_notes?: string;
  tags?: string[];
  is_favorite: boolean;
  is_verified: boolean;
  verification_source?: 'community' | 'expert' | 'ai';
}

interface ScanState {
  scans: ScanResult[];
  currentScan: ScanResult | null;
  isScanning: boolean;
  isProcessing: boolean;
  scanHistory: ScanResult[];
  favorites: ScanResult[];
  
  // Filters and search
  searchQuery: string;
  categoryFilter: string | null;
  dateFilter: { start?: Date; end?: Date } | null;
  
  // Sync status
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  
  // Actions
  initialize: () => Promise<void>;
  startScan: () => void;
  stopScan: () => void;
  setProcessing: (processing: boolean) => void;
  addScan: (scan: ScanResult) => Promise<void>;
  updateScan: (id: string, updates: Partial<ScanResult>) => Promise<void>;
  deleteScan: (id: string) => Promise<void>;
  toggleFavorite: (id: string) => Promise<void>;
  setCurrentScan: (scan: ScanResult | null) => void;
  
  // Search and filter
  setSearchQuery: (query: string) => void;
  setCategoryFilter: (category: string | null) => void;
  setDateFilter: (filter: { start?: Date; end?: Date } | null) => void;
  getFilteredScans: () => ScanResult[];
  loadScans: (userId: string) => Promise<void>;
  searchScans: (userId: string, query: string, category?: string) => Promise<void>;
  
  // Sync
  forcSync: () => Promise<void>;
}

export const useScanStore = create<ScanState>()(
  persist(
    (set, get) => ({
      scans: [],
      currentScan: null,
      isScanning: false,
      isProcessing: false,
      scanHistory: [],
      favorites: [],
      searchQuery: '',
      categoryFilter: null,
      dateFilter: null,
      isOnline: false,
      isSyncing: false,
      lastSyncTime: null,

      initialize: async () => {
        try {
          // Initialize sync service and listen for status changes
          await syncService.initialize();
          
          syncService.onStatusChange((status) => {
            set({
              isOnline: status.isOnline,
              isSyncing: status.isSyncing,
              lastSyncTime: status.lastSyncTime,
            });
          });

          console.log('Scan store initialized');
        } catch (error) {
          console.error('Scan store initialization error:', error);
        }
      },

      startScan: () => set({ isScanning: true }),
      
      stopScan: () => set({ isScanning: false }),
      
      setProcessing: (processing: boolean) => set({ isProcessing: processing }),
      
      addScan: async (scan: ScanResult) => {
        try {
          // Add to local database (Turso)
          await tursoService.insertScan(scan);
          
          // Update in-memory state
          const { scans } = get();
          const newScans = [scan, ...scans];
          set({
            scans: newScans,
            scanHistory: newScans,
            currentScan: scan,
          });

          // Trigger sync if online
          if (get().isOnline) {
            syncService.triggerSync();
          }
        } catch (error) {
          console.error('Error adding scan:', error);
          throw error;
        }
      },
      
      updateScan: async (id: string, updates: Partial<ScanResult>) => {
        try {
          // Update in local database
          await tursoService.updateScan(id, updates);
          
          // Update in-memory state
          const { scans } = get();
          const updatedScans = scans.map(scan =>
            scan.id === id ? { ...scan, ...updates } : scan
          );
          set({
            scans: updatedScans,
            scanHistory: updatedScans,
          });

          // Trigger sync if online
          if (get().isOnline) {
            syncService.triggerSync();
          }
        } catch (error) {
          console.error('Error updating scan:', error);
          throw error;
        }
      },
      
      deleteScan: async (id: string) => {
        try {
          // Delete from local database
          await tursoService.deleteScan(id);
          
          // Update in-memory state
          const { scans } = get();
          const filteredScans = scans.filter(scan => scan.id !== id);
          set({
            scans: filteredScans,
            scanHistory: filteredScans,
          });

          // Trigger sync if online
          if (get().isOnline) {
            syncService.triggerSync();
          }
        } catch (error) {
          console.error('Error deleting scan:', error);
          throw error;
        }
      },
      
      toggleFavorite: async (id: string) => {
        const { scans } = get();
        const scan = scans.find(s => s.id === id);
        if (!scan) return;

        const updates = { is_favorite: !scan.is_favorite };
        await get().updateScan(id, updates);
        
        // Update favorites list
        const updatedScans = get().scans;
        const favorites = updatedScans.filter(scan => scan.is_favorite);
        set({ favorites });
      },
      
      setCurrentScan: (scan: ScanResult | null) => set({ currentScan: scan }),
      
      setSearchQuery: (query: string) => set({ searchQuery: query }),
      
      setCategoryFilter: (category: string | null) => set({ categoryFilter: category }),
      
      setDateFilter: (filter: { start?: Date; end?: Date } | null) => set({ dateFilter: filter }),
      
      getFilteredScans: () => {
        const { scans, searchQuery, categoryFilter, dateFilter } = get();
        
        return scans.filter(scan => {
          // Search query filter
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const matchesName = scan.identification.name.toLowerCase().includes(query);
            const matchesScientific = scan.identification.scientific_name?.toLowerCase().includes(query);
            const matchesNotes = scan.user_notes?.toLowerCase().includes(query);
            const matchesTags = scan.tags?.some(tag => tag.toLowerCase().includes(query));
            
            if (!matchesName && !matchesScientific && !matchesNotes && !matchesTags) {
              return false;
            }
          }
          
          // Category filter
          if (categoryFilter && scan.category !== categoryFilter) {
            return false;
          }
          
          // Date filter
          if (dateFilter) {
            const scanDate = new Date(scan.metadata.timestamp);
            if (dateFilter.start && scanDate < dateFilter.start) return false;
            if (dateFilter.end && scanDate > dateFilter.end) return false;
          }
          
          return true;
        });
      },

      loadScans: async (userId: string) => {
        try {
          const scans = await tursoService.getScans(userId);
          const favorites = scans.filter(scan => scan.is_favorite);
          
          set({
            scans,
            scanHistory: scans,
            favorites,
          });
        } catch (error) {
          console.error('Error loading scans:', error);
        }
      },

      searchScans: async (userId: string, query: string, category?: string) => {
        try {
          const results = await tursoService.searchScans(userId, query, category);
          set({ scans: results });
        } catch (error) {
          console.error('Error searching scans:', error);
        }
      },
      
      forcSync: async () => {
        try {
          await syncService.forcSync();
        } catch (error) {
          console.error('Force sync error:', error);
          throw error;
        }
      },
    }),
    {
      name: 'scan-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // Only persist essential data, not the full scans array
        // since that's stored in Turso
        searchQuery: state.searchQuery,
        categoryFilter: state.categoryFilter,
        dateFilter: state.dateFilter,
      }),
    }
  )
);
