import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { ThemePresets, ThemeVariant, ApexTheme } from './theme';
import { useAuthStore } from '../stores/authStoreFirebase';

interface ThemeContextType {
  currentTheme: ThemeVariant;
  setTheme: (theme: ThemeVariant) => void;
  paperTheme: ApexTheme['paper'];
  navigationTheme: ApexTheme['navigation'];
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ApexThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const { user } = useAuthStore();
  
  // Get theme preference from user settings or default to auto
  const userThemePreference = user?.preferences?.theme || 'auto';
  
  const [currentTheme, setCurrentTheme] = useState<ThemeVariant>(() => {
    if (userThemePreference === 'auto') {
      return systemColorScheme === 'dark' ? 'dark' : 'light';
    }
    return userThemePreference as ThemeVariant;
  });

  // Update theme when system color scheme or user preference changes
  useEffect(() => {
    if (userThemePreference === 'auto') {
      setCurrentTheme(systemColorScheme === 'dark' ? 'dark' : 'light');
    } else if (userThemePreference in ThemePresets) {
      setCurrentTheme(userThemePreference as ThemeVariant);
    }
  }, [systemColorScheme, userThemePreference]);

  const setTheme = async (theme: ThemeVariant) => {
    setCurrentTheme(theme);
    
    // Update user preferences if authenticated
    if (user) {
      try {
        await useAuthStore.getState().updateProfile({
          preferences: {
            ...user.preferences,
            theme: theme,
          },
        });
      } catch (error) {
        console.error('Failed to update theme preference:', error);
      }
    }
  };

  const paperTheme = ThemePresets[currentTheme].paper;
  const navigationTheme = ThemePresets[currentTheme].navigation;
  const isDark = currentTheme === 'dark';

  const contextValue: ThemeContextType = {
    currentTheme,
    setTheme,
    paperTheme,
    navigationTheme,
    isDark,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <PaperProvider theme={paperTheme}>
        <NavigationThemeProvider value={navigationTheme}>
          {children}
        </NavigationThemeProvider>
      </PaperProvider>
    </ThemeContext.Provider>
  );
};
