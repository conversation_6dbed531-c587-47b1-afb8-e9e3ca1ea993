import { MD3LightTheme, MD3DarkTheme, configureFonts } from 'react-native-paper';
import { DefaultTheme, DarkTheme } from '@react-navigation/native';

// Custom color palettes for the 5 preset themes
export const ThemeColors = {
  light: {
    primary: '#2E7D32',      // Green primary
    secondary: '#FF6F00',    // Orange secondary
    tertiary: '#1976D2',     // Blue tertiary
    surface: '#FFFFFF',
    background: '#F5F5F5',
    onSurface: '#1C1B1F',
    onBackground: '#1C1B1F',
    outline: '#79747E',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
  },
  dark: {
    primary: '#4CAF50',      // Lighter green for dark mode
    secondary: '#FFB74D',    // Lighter orange
    tertiary: '#64B5F6',     // Lighter blue
    surface: '#1C1B1F',
    background: '#121212',
    onSurface: '#E6E1E5',
    onBackground: '#E6E1E5',
    outline: '#938F99',
    success: '#66BB6A',
    warning: '#FFB74D',
    error: '#EF5350',
    info: '#42A5F5',
  },
  nature: {
    primary: '#2E7D32',      // Forest green
    secondary: '#8BC34A',    // Light green
    tertiary: '#795548',     // Brown
    surface: '#F1F8E9',
    background: '#E8F5E8',
    onSurface: '#1B5E20',
    onBackground: '#1B5E20',
    outline: '#4CAF50',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
  },
  ocean: {
    primary: '#0277BD',      // Ocean blue
    secondary: '#00ACC1',    // Cyan
    tertiary: '#26C6DA',     // Light cyan
    surface: '#E1F5FE',
    background: '#E0F2F1',
    onSurface: '#01579B',
    onBackground: '#01579B',
    outline: '#0288D1',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#03A9F4',
  },
  sunset: {
    primary: '#E65100',      // Deep orange
    secondary: '#FF5722',    // Orange red
    tertiary: '#FF9800',     // Orange
    surface: '#FFF3E0',
    background: '#FFF8E1',
    onSurface: '#BF360C',
    onBackground: '#BF360C',
    outline: '#FF6F00',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
  },
};

// Font configuration
const fontConfig = {
  web: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400' as const,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100' as const,
    },
  },
  ios: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400' as const,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100' as const,
    },
  },
  android: {
    regular: {
      fontFamily: 'sans-serif',
      fontWeight: 'normal' as const,
    },
    medium: {
      fontFamily: 'sans-serif-medium',
      fontWeight: 'normal' as const,
    },
    light: {
      fontFamily: 'sans-serif-light',
      fontWeight: 'normal' as const,
    },
    thin: {
      fontFamily: 'sans-serif-thin',
      fontWeight: 'normal' as const,
    },
  },
};

// Create theme variants
export const createTheme = (variant: keyof typeof ThemeColors, isDark: boolean = false) => {
  const colors = ThemeColors[variant];
  const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
  
  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      ...colors,
    },
    fonts: configureFonts({ config: fontConfig }),
  };
};

// Navigation themes
export const createNavigationTheme = (variant: keyof typeof ThemeColors, isDark: boolean = false) => {
  const colors = ThemeColors[variant];
  const baseTheme = isDark ? DarkTheme : DefaultTheme;
  
  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: colors.primary,
      background: colors.background,
      card: colors.surface,
      text: colors.onSurface,
      border: colors.outline,
      notification: colors.secondary,
    },
  };
};

// Theme presets
export const ThemePresets = {
  light: {
    paper: createTheme('light', false),
    navigation: createNavigationTheme('light', false),
  },
  dark: {
    paper: createTheme('dark', true),
    navigation: createNavigationTheme('dark', true),
  },
  nature: {
    paper: createTheme('nature', false),
    navigation: createNavigationTheme('nature', false),
  },
  ocean: {
    paper: createTheme('ocean', false),
    navigation: createNavigationTheme('ocean', false),
  },
  sunset: {
    paper: createTheme('sunset', false),
    navigation: createNavigationTheme('sunset', false),
  },
};

// Theme context type
export type ThemeVariant = keyof typeof ThemePresets;
export type ApexTheme = typeof ThemePresets.light;

// Common styles
export const commonStyles = {
  container: {
    flex: 1,
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 16,
  },
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  spaceBetween: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  input: {
    borderRadius: 8,
    marginVertical: 8,
  },
};
